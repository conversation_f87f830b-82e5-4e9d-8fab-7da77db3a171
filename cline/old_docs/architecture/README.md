# Cline Extension Architecture

Questa documentazione descrive la nuova architettura modulare di Cline e MATRIX IDE, basata su upstream puliti e modularità plugin.

## Strategia Upstream e Modularità

- **Upstream puliti**: la base del sistema è costituita da Floem 0.2 (UI), <PERSON><PERSON><PERSON> (editor) e <PERSON>line (moduli upstream), integrati senza modifiche invasive.
- **Modularità plugin**: ogni plugin/MCP è sviluppato come modulo indipendente, facilmente integrabile e aggiornabile.
- **Adattamento moduli Cline**: i moduli Cline vengono selezionati/adattati per estendere le funzionalità mantenendo il core snello.

## Blueprint Architetturale

Il file [extension-architecture.mmd](./extension-architecture.mmd) contiene un diagramma Mermaid che illustra la relazione tra:
- Upstream (Floem 0.2, La<PERSON>ce, Cline)
- Core (Cline Core Engine, Plugin System, UI Layer, MCP Integration)
- Moduli <PERSON>line (moduli funzionali adattati)
- Plugin/MCP (moduli indipendenti)

Il flusso architetturale mostra come i moduli upstream alimentano il core, che a sua volta carica dinamicamente moduli Cline e plugin/MCP.

## Visualizzazione del Diagramma

Per visualizzare il diagramma:
1. Installa un'estensione Mermaid per VSCode
2. Apri extension-architecture.mmd
3. Usa la funzione di anteprima per renderizzare il diagramma

Il diagramma è compatibile anche con la visualizzazione nativa di GitHub.

## Schema colori
## Struttura `/plugins` e Modularità

Tutti i plugin di MATRIX IDE sono collocati nella directory `/plugins`, ciascuno come crate Rust indipendente e membro del workspace Rust principale.
**Tutti i plugin condividono lo stesso lockfile (`Cargo.lock`) per garantire coerenza e build riproducibili.**

Ogni plugin:
- Ha dipendenze solo verso upstream (floem, lapce, cline) e nessuna dipendenza locale.
- Utilizza versioni stabili e uniformi di `lapce` e `cline`, definite centralmente nel workspace.
- Le dipendenze comuni (es. `parking_lot`) sono allineate su una versione unica per tutto il workspace.
- È sviluppato e testato in modo isolato, favorendo aggiornabilità e manutenibilità.
- Viene caricato dinamicamente dal core tramite il sistema plugin.

Questa organizzazione garantisce un core minimale e facilmente estendibile, senza accoppiamenti tra plugin o con il core stesso.

Il diagramma utilizza colori ad alto contrasto per distinguere i livelli architetturali e i flussi tra upstream, core, moduli e plugin.
