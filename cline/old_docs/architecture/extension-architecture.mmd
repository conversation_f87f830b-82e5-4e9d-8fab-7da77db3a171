flowchart TB
    subgraph "Upstream"
        Floem["Floem 0.2"]
        <PERSON><PERSON><PERSON>["<PERSON><PERSON><PERSON>"]
        ClineUp["Cline (moduli upstream)"]
    end

    subgraph "Cline Core"
        CoreEngine["Core Engine"]
        PluginSystem["Plugin System<br/>(moduli indipendenti)"]
        UILayer["UI Layer<br/>(<PERSON><PERSON><PERSON>+<PERSON>lo<PERSON>)"]
        MCPIntegration["MCP Integration"]
    end

    subgraph "Moduli Cline"
        ClineMod1["Modulo Cline A"]
        ClineMod2["Modulo Cline B"]
        ClineModN["Modulo Cline N"]
    end

    subgraph "Plugin/MCP"
        PluginA["Plugin/MCP A"]
        PluginB["Plugin/MCP B"]
        PluginN["Plugin/MCP N"]
    end

    %% Flussi architetturali
    Floem --> UILayer
    Lapce --> UILayer
    ClineUp --> ClineMod1
    ClineUp --> ClineMod2
    ClineUp --> <PERSON>lineModN
    UILayer --> CoreEngine
    CoreEngine --> PluginSystem
    PluginSystem --> ClineMod1
    PluginSystem --> ClineMod2
    PluginSystem --> ClineModN
    ClineMod1 --> PluginA
    ClineMod2 --> PluginB
    ClineModN --> PluginN
    PluginSystem --> MCPIntegration
    MCPIntegration --> PluginA
    MCPIntegration --> PluginB
    MCPIntegration --> PluginN

    %% Relazioni legacy (VSCode Extension Host)
    subgraph "VSCode Extension Host"
        ExtensionEntry["Extension Entry<br/>src/extension.ts"]
        WebviewProvider["WebviewProvider<br/>src/core/webview/index.ts"]
        Controller["Controller<br/>src/core/controller/index.ts"]
        Task["Task<br/>src/core/task/index.ts"]
        McpHub["McpHub<br/>src/services/mcp/McpHub.ts"]
    end
    ExtensionEntry --> WebviewProvider
    WebviewProvider --> Controller
    Controller --> Task
    Controller --> McpHub

    classDef vscodeState fill:#f9f,stroke:#333,stroke-width:2px
    classDef contextClass fill:#bbf,stroke:#333,stroke-width:2px
    classDef providerClass fill:#bfb,stroke:#333,stroke-width:2px
    classDef apiClass fill:#fdb,stroke:#333,stroke-width:2px

    class GlobalState,SecretsStorage vscodeState
    class ExtStateContext contextClass
    class WebviewProvider,McpHub providerClass
    class AnthropicAPI,OpenRouterAPI,BedrockAPI,OtherAPIs apiClass
