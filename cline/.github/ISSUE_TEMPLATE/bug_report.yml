name: 🐛 Bug Report
description: File a bug report
labels: ["bug"]
body:
    - type: markdown
      attributes:
          value: |
              **Important:** All bug reports must be reproducible using Claude 3.5 Sonnet. Cline uses complex prompts so less capable models may not work as expected.
    - type: textarea
      id: what-happened
      attributes:
          label: What happened?
          description: Also tell us, what did you expect to happen?
          placeholder: Tell us what you see!
      validations:
          required: true
    - type: textarea
      id: steps
      attributes:
          label: Steps to reproduce
          description: How do you trigger this bug? Please walk us through it step by step.
          value: |
              1.
              2.
              3.
      validations:
          required: true
    - type: textarea
      id: logs
      attributes:
          label: Relevant API REQUEST output
          description: Please copy and paste any relevant output. This will be automatically formatted into code, so no need for backticks.
          render: shell
    - type: input
      id: provider-model
      attributes:
          label: Provider/Model
          description: What provider and model were you using when the issue occurred?
          placeholder: "e.g., cline:anthropic/claude-3.7-sonnet, gemini:gemini-2.5-pro-exp-03-25"
      validations:
          required: true
    - type: input
      id: operating-system
      attributes:
          label: Operating System
          description: What operating system are you using?
          placeholder: "e.g., Windows 11, macOS Sonoma, Ubuntu 22.04"
      validations:
          required: true
    - type: textarea
      id: system-info
      attributes:
          label: System Info
          description: What system information is relevant to the issue?
          placeholder: "e.g., CPU: Intel Core i7-11700K, GPU: NVIDIA GeForce RTX 3070, RAM: 32GB DDR4"
      validations:
          required: true
    - type: input
      id: cline-version
      attributes:
          label: Cline Version
          description: What version of Cline are you using? (You can find this at the bottom of the Settings view)
          placeholder: "e.g., 1.2.3"
      validations:
          required: true
    - type: textarea
      id: additional-context
      attributes:
          label: Additional context
          description: Add any other context about the problem here, such as screenshots or related issues.
