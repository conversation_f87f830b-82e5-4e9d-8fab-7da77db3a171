Cline Rules allow you to provide <PERSON><PERSON> with system-level guidance. Think of them as a persistent way to include context and preferences for your projects or globally for every conversation.

## Creating a Rule

You can create a rule by clicking the `+` button in the Rules tab. This will open a new file in your IDE which you can use to write your rule.

<Frame>
	<img src="https://storage.googleapis.com/cline_public_images/docs/assets/cline-rules.png" alt="Create a Rule" />
</Frame>

Once you save the file:

-   Your rule will be stored in the `.clinerules/` directory in your project (if it's a Workspace Rule)
-   Or in the `Documents/Cline/Rules` directory (if it's a Global Rule).

You can also have <PERSON><PERSON> create a rule for you by using the [`/newrule` slash command](/features/slash-commands/new-rule) in the chat.

```markdown Example Cline Rule Structure [expandable]
# Project Guidelines

## Documentation Requirements

-   Update relevant documentation in /docs when modifying features
-   Keep README.md in sync with new capabilities
-   Maintain changelog entries in CHANGELOG.md

## Architecture Decision Records

Create ADRs in /docs/adr for:

-   Major dependency changes
-   Architectural pattern changes
-   New integration patterns
-   Database schema changes
    Follow template in /docs/adr/template.md

## Code Style & Patterns

-   Generate API clients using OpenAPI Generator
-   Use TypeScript axios template
-   Place generated code in /src/generated
-   Prefer composition over inheritance
-   Use repository pattern for data access
-   Follow error handling pattern in /src/utils/errors.ts

## Testing Standards

-   Unit tests required for business logic
-   Integration tests for API endpoints
-   E2E tests for critical user flows
```

### Key Benefits

1. **Version Controlled**: The `.clinerules` file becomes part of your project's source code
2. **Team Consistency**: Ensures consistent behavior across all team members
3. **Project-Specific**: Rules and standards tailored to each project's needs
4. **Institutional Knowledge**: Maintains project standards and practices in code

Place the `.clinerules` file in your project's root directory:

```
your-project/
├── .clinerules
├── src/
├── docs/
└── ...
```

Cline's system prompt, on the other hand, is not user-editable ([here's where you can find it](https://github.com/cline/cline/blob/main/src/core/prompts/system.ts)). For a broader look at prompt engineering best practices, check out [this resource](https://docs.anthropic.com/en/docs/build-with-claude/prompt-engineering/overview).

### Tips for Writing Effective Cline Rules

-   Be Clear and Concise: Use simple language and avoid ambiguity.
-   Focus on Desired Outcomes: Describe the results you want, not the specific steps.
-   Test and Iterate: Experiment to find what works best for your workflow.

### .clinerules/ Folder System

```
your-project/
├── .clinerules/              # Folder containing active rules
│   ├── 01-coding.md          # Core coding standards
│   ├── 02-documentation.md   # Documentation requirements
│   └── current-sprint.md     # Rules specific to current work
├── src/
└── ...
```

Cline automatically processes **all Markdown files** inside the `.clinerules/` directory, combining them into a unified set of rules. The numeric prefixes (optional) help organize files in a logical sequence.

#### Using a Rules Bank

For projects with multiple contexts or teams, maintain a rules bank directory:

```
your-project/
├── .clinerules/              # Active rules - automatically applied
│   ├── 01-coding.md
│   └── client-a.md
│
├── clinerules-bank/          # Repository of available but inactive rules
│   ├── clients/              # Client-specific rule sets
│   │   ├── client-a.md
│   │   └── client-b.md
│   ├── frameworks/           # Framework-specific rules
│   │   ├── react.md
│   │   └── vue.md
│   └── project-types/        # Project type standards
│       ├── api-service.md
│       └── frontend-app.md
└── ...
```

#### Benefits of the Folder Approach

1. **Contextual Activation**: Copy only relevant rules from the bank to the active folder
2. **Easier Maintenance**: Update individual rule files without affecting others
3. **Team Flexibility**: Different team members can activate rules specific to their current task
4. **Reduced Noise**: Keep the active ruleset focused and relevant

#### Usage Examples

Switch between client projects:

```bash
# Switch to Client B project
rm .clinerules/client-a.md
cp clinerules-bank/clients/client-b.md .clinerules/
```

Adapt to different tech stacks:

```bash
# Frontend React project
cp clinerules-bank/frameworks/react.md .clinerules/
```

#### Implementation Tips

-   Keep individual rule files focused on specific concerns
-   Use descriptive filenames that clearly indicate the rule's purpose
-   Consider git-ignoring the active `.clinerules/` folder while tracking the `clinerules-bank/`
-   Create team scripts to quickly activate common rule combinations

The folder system transforms your Cline rules from a static document into a dynamic knowledge system that adapts to your team's changing contexts and requirements.

### Managing Rules with the Toggleable Popover

To make managing both single `.clinerules` files and the folder system even easier, Cline v3.13 introduces a dedicated popover UI directly accessible from the chat interface.

Located conveniently under the chat input field, this popover allows you to:

-   **Instantly See Active Rules:** View which global rules (from your user settings) and workspace rules (`.clinerules` file or folder contents) are currently active.
-   **Quickly Toggle Rules:** Enable or disable specific rule files within your workspace `.clinerules/` folder with a single click. This is perfect for activating context-specific rules (like `react-rules.md` or `memory-bank.md`) only when needed.
-   **Easily Add/Manage Rules:** Quickly create a workspace `.clinerules` file or folder if one doesn't exist, or add new rule files to an existing folder.

This UI significantly simplifies switching contexts and managing different sets of instructions without needing to manually edit files or configurations during a conversation.

<Frame>
	<img src="https://storage.googleapis.com/cline_public_images/docs/assets/image%20(1).png" alt="Cline Logo" />
</Frame>
