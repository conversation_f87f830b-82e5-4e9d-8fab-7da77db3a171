---
title: "Our Favorite Tech Stack"
description: "A curated list of our recommended technologies and tools for building modern web applications with Cline."
---

## Recommended Stack for New Cline Users (2025)

### Your Complete Development Environment

#### Development Tools

-   **VS Code** - Your code editor, [download here](https://code.visualstudio.com/)
-   **GitHub** - Where your code lives, [sign up here](https://github.com)

#### Frontend

-   **Next.js 14+** - React framework with App Router
-   **Tailwind CSS** - Beautiful styling without writing CSS
-   **TypeScript** - JavaScript, but safer and smarter

#### Backend

-   **Supabase** - Your complete backend solution, [sign up with GitHub](https://supabase.com)
    -   PostgreSQL database
    -   Authentication
    -   File storage
    -   Real-time updates

#### Deployment

-   **Vercel** - Where your app runs, [sign up with GitHub](https://vercel.com)
    -   Automatic deployments from GitHub
    -   Preview deployments for testing
    -   Production-ready CDN

#### AI Development

Choose your AI assistant based on your needs:

| Model             | Input Cost (per 1M tokens) | Output Cost (per 1M tokens) | Best For                       |
| ----------------- | -------------------------- | --------------------------- | ------------------------------ |
| Claude 3.5 Sonnet | $3.00                      | $15.00                      | Production apps, complex tasks |
| DeepSeek R1       | $1.00                      | $3.00                       | Budget-conscious production    |
| DeepSeek V3       | $0.14                      | $2.20                       | Budget-conscious development   |

#### Free Tier Benefits

**Vercel (Hobby)**

-   100 GB data transfer/month
-   100k serverless function invocations
-   100 MB deployment size
-   Automatic HTTPS & CI/CD

**Supabase (Free)**

-   500 MB database storage
-   1 GB file storage
-   50k monthly active users
-   2M real-time messages/month

**GitHub (Free)**

-   Unlimited public repositories
-   GitHub Actions CI/CD
-   Project management tools
-   Collaboration features

### Getting Started

1. Install the development essentials:
    - Follow our [Development Essentials Installation Guide](https://docs.cline.bot/getting-started/installing-dev-essentials)
2. Set up Cline's Memory Bank:
    - Follow the [Memory Bank setup instructions](https://docs.cline.bot/prompting/cline-memory-bank)
    - Create an empty `cline_docs` folder in your project root
    - Create `projectBrief.md` in the `cline_docs` folder (see example below)
    - Tell Cline to "initialize memory bank"
3. Add our recommended stack configuration:
    - Create `.clinerules` file (see template below)
    - Let Cline handle the rest!

#### Example Project Brief

```markdown
# Project Brief

## Overview

Building a [type of application] that will [main purpose].

## Core Features

-   Feature 1
-   Feature 2
-   Feature 3

## Target Users

[Describe who will use your application]

## Technical Preferences (optional)

-   Any specific technologies you want to use
-   Any specific requirements or constraints
```

### .clinerules Template

```markdown
# Project Configuration

## Tech Stack

-   Next.js 14+ with App Router
-   Tailwind CSS for styling
-   Supabase for backend
-   Vercel for deployment
-   GitHub for version control

## Project Structure

/src
/app # Next.js App Router pages
/components # React components
/lib # Utility functions
/types # TypeScript types
/supabase
/migrations # SQL migration files
/seed # Seed data files
/public # Static assets

## Database Migrations

SQL files in /supabase/migrations should:

-   Use sequential numbering: 001, 002, etc.
-   Include descriptive names
-   Be reviewed by Cline before execution
    Example: 001_create_users_table.sql

## Development Workflow

-   Cline helps write and review code changes
-   Vercel automatically deploys from main branch
-   Database migrations reviewed by Cline before execution

## Security

DO NOT read or modify:

-   .env files
-   \*_/config/secrets._
-   Any file containing API keys or credentials
```

### Learning Resources (2025)

Want to learn more about the technologies we're using? Here are some great resources:

#### Next.js and React

-   [Official Learn Next.js Course](https://nextjs.org/learn) - Interactive tutorial
-   [NextJS App Router: Modern Web Dev in 1 Hour](https://www.youtube.com/nextjs-modern) - Quick overview
-   [Building Real-World Apps with Next.js](https://www.youtube.com/nextjs-real-world) - Practical examples

#### Supabase

-   [Supabase From Scratch](https://www.udemy.com/supabase-scratch) - Comprehensive course
-   [Official Quickstart Guides](https://supabase.com/docs/guides/getting-started)
-   [Real-Time Apps with Next.js and Supabase](https://www.newline.co/courses/supabase-nextjs)

#### Tailwind CSS

-   [Tailwind CSS Tutorial for Beginners](https://www.youtube.com/tailwind-2025)
-   [Official Tailwind Documentation](https://tailwindcss.com/docs)
-   Interactive course at [Scrimba Tailwind CSS Course](https://scrimba.com/learn/tailwind)

### Other Things to Know

#### Working with Git & GitHub

Git helps you track changes in your code and collaborate with others. Here are the essential commands you'll use:

**Daily Development**

```bash
# Save your changes (do this often!)
git add .                                    # Stage all changed files
git commit -m "Add login page"              # Save changes with a clear message

# Share your changes
git push origin main                        # Upload to GitHub
```

**Common Workflow**

1.  **Start of day**: Get latest changes

    ```bash
    git pull origin main                     # Download latest code
    ```

2.  **During development**: Save work regularly

    ```bash
    git add .
    git commit -m "Clear message about changes"
    ```

3.  **End of day**: Share your progress

    ```bash
    git push origin main                     # Upload to GitHub
    ```

**Best Practices**

-   Commit often with clear messages
-   Pull before starting new work
-   Push completed work to share with others
-   Use `.gitignore` to avoid committing sensitive files

> **Tip**: Vercel automatically deploys when you push to main!

#### Environment Variables

-   Store secrets in `.env.local` for development
-   Add them to Vercel project settings for production
-   Never commit `.env` files to Git

#### Getting Help

1. Use `/help` in Cline chat for immediate assistance
2. Check [Cline Documentation](https://docs.cline.bot)
3. Join our [Discord Community](https://discord.gg/cline)
4. Search GitHub issues for common problems

Remember: Cline is here to help at every step. Just ask for guidance or clarification when needed!
