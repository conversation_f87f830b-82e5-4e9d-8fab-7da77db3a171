---
title: "For New Coders"
description: "Welcome to <PERSON><PERSON>, your AI-powered coding companion! This guide will help you quickly set up your development environment and begin your coding journey with ease."
---

> 💡 **Tip:** If you're completely new to coding, take your time with each step. There's no rush — <PERSON><PERSON> is here to guide you!

### 🚀 Getting Started

Before you jump into coding, make sure you have these essentials ready:

#### 1. **VS Code**

A popular, free, and powerful code editor.

-   [<u>Download VS Code</u>](https://code.visualstudio.com/)

📺 **Recommended YouTube Tutorial:** [<u>How to Install VS Code</u>](https://www.youtube.com/watch?v=MlIzFUI1QGA)

> ✅ **Pro Tip:** Install VS Code in your Applications folder (macOS) or Program Files (Windows) for easy access from your dock or start menu.

#### 2. **Organize Your Projects**

Create a dedicated folder named `Cline` in your Documents folder for all your coding projects:

-   **macOS:** `/Users/<USER>/Documents/Cline`
-   **Windows:** `C:\Users\<USER>\Documents\Cline`

Inside your `Cline` folder, structure projects clearly:

-   `Documents/Cline/workout-app` _(e.g., for a fitness tracking app)_
-   `Documents/Cline/portfolio-website` _(e.g., to showcase your work)_

> 💡 **Tip:** Keeping your projects organized from the start will save you time and confusion later!

#### 3. **Install the Cline VS Code Extension**

Enhance your coding workflow by installing the Cline extension directly within VS Code:

-   Get Started with Cline Extension Tutorial

📺 **Recommended YouTube Tutorial:** [<u>How To Install Extensions in VS Code</u>](https://www.youtube.com/watch?v=E7trgwZa-mk)

> ✅ **Pro Tip:** After installing, reload VS Code to ensure the extension is activated properly.

#### 4. **Essential Development Tools**

Basic software required for coding efficiently:

-   Homebrew (macOS)
-   Node.js
-   Git

👉 [<u>Follow our detailed guide on Installing Essential Development Tools with step-by-step help from Cline.</u>](https://docs.cline.bot/getting-started/installing-dev-essentials#installing-dev-essentials)

📺 **Recommended YouTube Tutorials for Manual Installation:**

-   **For macOS:**
    -   [<u>Install Homebrew on Mac</u>](https://www.youtube.com/watch?v=hwGNgVbqasc)
    -   [<u>Install Git on macOS 2024</u>](https://www.youtube.com/watch?v=B4qsvQ5IqWk)
    -   [<u>Install Node.js on Mac (M1 | M2 | M3)</u>](https://www.youtube.com/watch?v=I8H4wolRFBk)
-   **For Windows:**
    -   [<u>Install Git on Windows 10/11 (2024)</u>](https://www.youtube.com/watch?v=yjxv1HuRQy0)
    -   [<u>Install Node.js in Windows 10/11</u>](https://www.youtube.com/watch?v=uCgAuOYpJd0)

> ⚠️ **Note:** If you run into permission issues during installation, try running your terminal or command prompt as an administrator.

🎉 You're all set! Dive in and start coding smarter and faster with **Cline**.
