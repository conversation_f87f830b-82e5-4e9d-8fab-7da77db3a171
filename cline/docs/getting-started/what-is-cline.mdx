---
title: "What is <PERSON><PERSON>?"
description: "An introduction to <PERSON><PERSON>, your AI-powered development assistant in VS Code."
---

<PERSON><PERSON> is an AI development assistant which integrates with Microsoft Visual Studio Code. It provides an interface between your IDE and LLMs facilitating code development, increasing productivity and lowering the barrier to entry for new coders. Depending on permissions, <PERSON><PERSON> can read/write files, execute commands, use your web browser, and expand its capabilities with Model Context Protocol servers.

What makes <PERSON><PERSON> distinctive is its thoughtful approach to code generation and its extensive integration capabilities. Rather than simply generating code snippets, <PERSON><PERSON> collaborates with developers by planning solutions step-by-step, maintaining awareness of the entire development environment, and requiring explicit approval for all changes. It can understand large codebases, accelerate onboarding for new engineers, and connect with hundreds of tools through its Model Context Protocol Marketplace, enabling everything from streamlined project deployments to automated incident response—all through natural language commands.
