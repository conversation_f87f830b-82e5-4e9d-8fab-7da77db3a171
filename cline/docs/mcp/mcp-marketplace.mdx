---
title: "MCP Made Easy"
description: "Learn how to use the MCP Marketplace to discover, install, and configure MCP servers that enhance C<PERSON>'s capabilities with additional tools and resources."
---

## What's an MCP Server?

MCP servers are specialized extensions that enhance Cline's capabilities. They enable <PERSON><PERSON> to perform additional tasks like fetching web pages, processing images, accessing APIs, and much more.

## MCP Marketplace Walkthrough

The MCP Marketplace provides a one-click installation experience for hundreds of MCP servers across various categories.

### 1. Access the Marketplace

-   In Cline, click the "Extensions" button (square icon) in the top toolbar
-   The MCP marketplace will open, showing available servers by category

### 2. Browse and Select a Server

-   Browse servers by category (Search, File-systems, Browser-automation, Research-data, etc.)
-   Click on a server to see details about its capabilities and requirements

### 3. Install and Configure

-   Click the install button for your chosen server
-   If the server requires an API key (most do), <PERSON><PERSON> will guide you through:
    -   Where to get the API key
    -   How to enter it securely
-   The server will be added to your MCP settings automatically

### 4. Verify Installation

-   C<PERSON> will show confirmation when installation is complete
-   Check the server status in <PERSON><PERSON>'s MCP settings UI

### 5. Using Your New Server

-   After successful installation, <PERSON><PERSON> will automatically integrate the server's capabilities
-   You'll see new tools and resources available in <PERSON><PERSON>'s system prompt
-   Simply ask Cline to use the capabilities of your new server
-   Example: "Search the web for recent React updates using Perplexity"

**Corporate Users:** If you're using Cline in a corporate environment, ensure you have permission to install third-party MCP servers according to your organization's security policies.

## What Happens Behind the Scenes

When you install an MCP server, several things happen automatically:

### 1. Installation Process

-   The server code is cloned/installed to `/Users/<USER>/Documents/Cline/MCP/`
-   Dependencies are installed
-   The server is built (TypeScript/JavaScript compilation or Python package installation)

### 2. Configuration

-   The MCP settings file is updated with your server configuration
-   This file is located at: `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json`
-   Environment variables (like API keys) are securely stored
-   The server path is registered

### 3. Server Launch

-   Cline detects the configuration change
-   Cline launches your server as a separate process
-   Communication is established via stdio or HTTP

### 4. Integration with Cline

-   Your server's capabilities are added to Cline's system prompt
-   Tools become available via `use_mcp_tool` commands
-   Resources become available via `access_mcp_resource` commands
-   Cline can now use these capabilities when prompted by the user

## Troubleshooting

### System Requirements

Make sure your system meets these requirements:

-   **Node.js 18.x or newer**
    -   Check by running: `node --version`
    -   Install from: https://nodejs.org/
    -   Required for JavaScript/TypeScript implementations
-   **Python 3.10 or newer**
    -   Check by running: `python --version`
    -   Install from: https://python.org/
    -   Note: Some specialized implementations may require Python 3.11+
-   **UV Package Manager**
    -   Modern Python package manager for dependency isolation
    -   Install using:
        ```bash
        curl -LsSf https://astral.sh/uv/install.sh | sh
        ```
        Or: `pip install uv`
    -   Verify with: `uv --version`

If any of these commands fail or show older versions, please install/update before continuing!

### Common Installation Issues

-   Ensure your internet connection is stable
-   Check that you have the necessary permissions to install new software
-   Verify that the API key was entered correctly (if required)
-   Check the server status in the MCP settings UI for any error messages

### How to Remove an MCP Server

To completely remove a faulty MCP server:

1. Open the MCP settings file: `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json`
2. Delete the entire entry for your server from the `mcpServers` object
3. Save the file
4. Restart Cline

### I'm Still Getting an Error

If you're getting an error when using an MCP server, you can try the following:

-   Check the MCP settings file for errors
-   Use a Claude Sonnet model for installation
-   Verify that paths to your server's files are correct
-   Ensure all required environment variables are set
-   Check if another process is using the same port (for HTTP-based servers)
-   Try removing and reinstalling the server (remove from both the `cline_mcp_settings.json` file and the `/Users/<USER>/Documents/Cline/MCP/` directory)
-   Use a terminal and run the command with its arguments directly. This will allow you to see the same errors that Cline is seeing

## MCP Server Rules

Cline is already aware of your active MCP servers and what they are for, but when you have a lot of MCP servers enabled, it can be useful to define when to use each server.

Utilize a `.clinerules` file or custom instructions to support intelligent MCP server activation through keyword-based triggers, making Cline's tool selection more intuitive and context-aware.

### How MCP Rules Work

MCP Rules group your connected MCP servers into functional categories and define trigger keywords that activate them automatically when detected in your conversations with Cline.

```json
{
	"mcpRules": {
		"webInteraction": {
			"servers": ["firecrawl-mcp-server", "fetch-mcp"],
			"triggers": ["web", "scrape", "browse", "website"],
			"description": "Tools for web browsing and scraping"
		}
	}
}
```

### Configuration Structure

1. **Categories**: Group related servers (e.g., "webInteraction", "mediaAndDesign")
2. **Servers**: List server names in each category
3. **Triggers**: Keywords that activate these servers
4. **Description**: Human-readable category explanation

### Benefits of MCP Rules

-   **Contextual Tool Selection**: Cline selects appropriate tools based on conversation context
-   **Reduced Friction**: No need to manually specify which tool to use
-   **Organized Capabilities**: Logically group related tools and servers
-   **Prioritization**: Handle ambiguous cases with explicit priority ordering

### Example Usage

When you write "Can you scrape this website?", Cline detects "scrape" and "website" as triggers, automatically selecting web-related MCP servers.

For finance tasks like "What's Apple's stock price?", keywords like "stock" and "price" trigger finance-related servers.

### Quick Start Template

```json
{
	"mcpRules": {
		"category1": {
			"servers": ["server-name-1", "server-name-2"],
			"triggers": ["keyword1", "keyword2", "phrase1", "phrase2"],
			"description": "Description of what these tools do"
		},
		"category2": {
			"servers": ["server-name-3"],
			"triggers": ["keyword3", "keyword4", "phrase3"],
			"description": "Description of what these tools do"
		},
		"category3": {
			"servers": ["server-name-4", "server-name-5"],
			"triggers": ["keyword5", "keyword6", "phrase4"],
			"description": "Description of what these tools do"
		}
	},
	"defaultBehavior": {
		"priorityOrder": ["category1", "category2", "category3"],
		"fallbackBehavior": "Ask user which tool would be most appropriate"
	}
}
```

Add this to your `.clinerules` file or to your custom instructions to make Cline's MCP server selection more intuitive and context-aware.
