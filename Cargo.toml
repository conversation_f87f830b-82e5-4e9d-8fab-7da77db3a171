[workspace]
resolver = "2"
members = [
    "crates/agent-console-plugin",
    "crates/matrix-agent",
    "crates/matrix-agent-console",
    "crates/matrix-ai",
    "crates/matrix-core",
    "crates/matrix-data",
    "crates/matrix-graphics-api",
    "crates/matrix-plugin-loader",
    "crates/matrix-ui"
]

# Exclude problematic subdirectories
exclude = [
    "cline",
    "lapce",
    "node_modules",
    "target",
    "plugins"
]

[workspace.dependencies]
# Core dependencies - versioni allineate
tokio = { version = "1.46", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
thiserror = "1.0"
log = "0.4"
env_logger = "0.11"
async-trait = "0.1"
uuid = { version = "1.17", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# UI dependencies - allineate con upstream
floem = { version = "0.2", features = ["editor", "serde"] }
taffy = "0.4"

# Graphics dependencies - versioni compatibili
wgpu = "22.1"
peniko = "0.2"

# Plugin system dependencies
libloading = "0.8"
petgraph = "0.6"

# Data dependencies
dirs = "5.0"
notify = "6.1"

# Internal crate dependencies
matrix-core = { path = "crates/matrix-core" }
matrix-ui = { path = "crates/matrix-ui" }
matrix-ai = { path = "crates/matrix-ai" }
matrix-data = { path = "crates/matrix-data" }
matrix-agent = { path = "crates/matrix-agent" }
matrix-graphics-api = { path = "crates/matrix-graphics-api" }
matrix-plugin-loader = { path = "crates/matrix-plugin-loader" }

[workspace.package]
version = "0.1.0"
authors = ["MATRIX IDE Team"]
license = "MIT"
repository = "https://github.com/matrix-ide/matrix-next"

