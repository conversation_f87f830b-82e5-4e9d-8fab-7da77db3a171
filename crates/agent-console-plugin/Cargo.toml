[package]
name = "agent-console-plugin"
version = "0.1.0"
edition = "2021"
description = "Agent Console Plugin per MATRIX IDE"
authors = ["MATRIX Team"]

[dependencies]
matrix-core.workspace = true
matrix-ui.workspace = true
matrix-graphics-api.workspace = true
floem.workspace = true
tokio.workspace = true
async-trait.workspace = true
serde.workspace = true
serde_json.workspace = true
thiserror.workspace = true
log = "0.4"
chrono = "0.4"
uuid = { version = "1.2", features = ["v4", "serde"] }

[lib]
name = "agent_console_plugin"
crate-type = ["cdylib", "rlib"]
