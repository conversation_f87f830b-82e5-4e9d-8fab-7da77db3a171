[package]
name = "matrix-agent"
version = "0.1.0"
edition = "2021"
authors = ["Matrix Project"]
description = "Agent framework for Matrix, forked/adapted from Cline. Modular, event-driven, extensible."

[lib]
name = "matrix_agent"
path = "lib.rs"

[dependencies]
# Core dependencies
tokio = { version = "1.32", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.2", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
thiserror = "1.0"
log = "0.4"
async-trait = "0.1"

# Internal dependencies
matrix-core = { path = "../matrix-core" }