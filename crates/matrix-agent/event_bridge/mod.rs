//! Modulo Event Bridge
//! Gestisce l'integrazione e il bridging degli eventi tra agent, core e bus esterni.
//! Adattato da pattern event bus di Cline per massima compatibilità.

use std::sync::{Arc, RwLock};
use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use tokio::sync::mpsc;
use uuid::Uuid;

/// Rappresenta un evento agentico compatibile con Cline MCP
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentEvent {
    pub id: Uuid,
    pub event_type: String,
    pub payload: serde_json::Value,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub source: EventSource,
    pub target: Option<EventTarget>,
}

/// Sorgente dell'evento
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EventSource {
    ClineController,
    MatrixAgent,
    MatrixCore,
    Plugin(String),
    User,
}

/// Target dell'evento
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EventTarget {
    ClineController,
    MatrixAgent,
    MatrixCore,
    Plugin(String),
    Broadcast,
}

/// Handler per eventi
pub type EventHandler = Arc<dyn Fn(&AgentEvent) -> Result<(), EventBridgeError> + Send + Sync>;

/// Errori del bridge
#[derive(Debug, thiserror::Error)]
pub enum EventBridgeError {
    #[error("Serialization error: {0}")]
    SerializationError(#[from] serde_json::Error),
    #[error("Channel error: {0}")]
    ChannelError(String),
    #[error("Handler error: {0}")]
    HandlerError(String),
}

/// Event Bridge principale con compatibilità Cline MCP
pub struct EventBridge {
    /// Handlers registrati per tipo di evento
    handlers: Arc<RwLock<HashMap<String, Vec<EventHandler>>>>,
    /// Canale per eventi asincroni
    event_sender: mpsc::UnboundedSender<AgentEvent>,
    /// Receiver per processing interno
    event_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<AgentEvent>>>>,
    /// Storico eventi per debugging
    event_history: Arc<RwLock<Vec<AgentEvent>>>,
}

impl EventBridge {
    /// Crea un nuovo event bridge compatibile con Cline
    pub fn new() -> Self {
        let (sender, receiver) = mpsc::unbounded_channel();

        EventBridge {
            handlers: Arc::new(RwLock::new(HashMap::new())),
            event_sender: sender,
            event_receiver: Arc::new(RwLock::new(Some(receiver))),
            event_history: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Registra un handler per un tipo di evento
    pub fn register_handler(&self, event_type: &str, handler: EventHandler) -> Result<(), EventBridgeError> {
        let mut handlers = self.handlers.write().map_err(|e| {
            EventBridgeError::HandlerError(format!("Failed to acquire write lock: {}", e))
        })?;

        handlers.entry(event_type.to_string())
            .or_insert_with(Vec::new)
            .push(handler);

        Ok(())
    }

    /// Invia un evento al bus (compatibile con Cline MCP)
    pub fn send_event(&self, event: AgentEvent) -> Result<(), EventBridgeError> {
        // Salva nell'history per debugging
        {
            let mut history = self.event_history.write().map_err(|e| {
                EventBridgeError::HandlerError(format!("Failed to acquire history lock: {}", e))
            })?;
            history.push(event.clone());

            // Mantieni solo gli ultimi 1000 eventi
            if history.len() > 1000 {
                history.drain(0..100);
            }
        }

        // Invia al canale asincrono
        self.event_sender.send(event).map_err(|e| {
            EventBridgeError::ChannelError(format!("Failed to send event: {}", e))
        })?;

        Ok(())
    }

    /// Crea un evento da payload Cline MCP
    pub fn create_cline_event(
        &self,
        event_type: &str,
        payload: serde_json::Value,
        target: Option<EventTarget>,
    ) -> AgentEvent {
        AgentEvent {
            id: Uuid::new_v4(),
            event_type: event_type.to_string(),
            payload,
            timestamp: chrono::Utc::now(),
            source: EventSource::ClineController,
            target,
        }
    }

    /// Avvia il processing degli eventi
    pub async fn start_processing(&self) -> Result<(), EventBridgeError> {
        let receiver = {
            let mut receiver_guard = self.event_receiver.write().map_err(|e| {
                EventBridgeError::HandlerError(format!("Failed to acquire receiver lock: {}", e))
            })?;
            receiver_guard.take().ok_or_else(|| {
                EventBridgeError::HandlerError("Event processing already started".to_string())
            })?
        };

        let handlers = self.handlers.clone();

        tokio::spawn(async move {
            let mut receiver = receiver;
            while let Some(event) = receiver.recv().await {
                Self::process_event(&event, &handlers).await;
            }
        });

        Ok(())
    }

    /// Processa un singolo evento
    async fn process_event(
        event: &AgentEvent,
        handlers: &Arc<RwLock<HashMap<String, Vec<EventHandler>>>>,
    ) {
        let handlers_for_event = {
            let handlers_guard = match handlers.read() {
                Ok(guard) => guard,
                Err(_) => return,
            };

            handlers_guard.get(&event.event_type).cloned().unwrap_or_default()
        };

        for handler in handlers_for_event {
            if let Err(e) = handler(event) {
                log::error!("Event handler error for {}: {}", event.event_type, e);
            }
        }
    }

    /// Ottiene la cronologia eventi per debugging
    pub fn get_event_history(&self) -> Result<Vec<AgentEvent>, EventBridgeError> {
        let history = self.event_history.read().map_err(|e| {
            EventBridgeError::HandlerError(format!("Failed to acquire history lock: {}", e))
        })?;

        Ok(history.clone())
    }
}
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_send_and_get_event() {
        let mut bridge = EventBridge::new();
        let event = AgentEvent {
            event_type: "test".to_string(),
            payload: "payload".to_string(),
        };
        bridge.send_event(event);
        assert_eq!(bridge.get_events().len(), 1);
        assert_eq!(bridge.get_events()[0].event_type, "test");
    }
}