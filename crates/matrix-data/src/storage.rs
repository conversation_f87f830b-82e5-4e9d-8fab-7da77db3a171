//! Modulo per la gestione dello storage
//!
//! Fornisce funzionalità per salvare e caricare dati persistenti.

use crate::error::{DataError, DataResult};
use crate::DataConfig;
use std::path::{Path, PathBuf};
use std::fs;
use std::io::{Read, Write};
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};

/// Trait per i backend di storage
#[async_trait]
pub trait StorageBackend: Send + Sync {
    /// Salva dati nel backend
    async fn save(&self, key: &str, data: &[u8]) -> DataResult<()>;

    /// Carica dati dal backend
    async fn load(&self, key: &str) -> DataResult<Vec<u8>>;

    /// Verifica se una chiave esiste
    async fn exists(&self, key: &str) -> DataResult<bool>;

    /// Elimina una chiave
    async fn delete(&self, key: &str) -> DataResult<()>;

    /// Elenca tutte le chiavi con un prefisso
    async fn list_keys(&self, prefix: &str) -> DataResult<Vec<String>>;
}

/// Backend di storage basato su filesystem
pub struct FileSystemBackend {
    /// Directory di base per lo storage
    base_dir: PathBuf,
}

impl FileSystemBackend {
    /// Crea un nuovo backend di storage basato su filesystem
    pub fn new(base_dir: impl Into<PathBuf>) -> Self {
        Self {
            base_dir: base_dir.into(),
        }
    }

    /// Converte una chiave in un percorso di file
    fn key_to_path(&self, key: &str) -> PathBuf {
        // Sostituisci i caratteri non validi per i percorsi
        let safe_key = key.replace("/", "_").replace("\\", "_");
        self.base_dir.join(safe_key)
    }
}

#[async_trait]
impl StorageBackend for FileSystemBackend {
    async fn save(&self, key: &str, data: &[u8]) -> DataResult<()> {
        let path = self.key_to_path(key);

        // Crea le directory intermedie se necessario
        if let Some(parent) = path.parent() {
            fs::create_dir_all(parent)
                .map_err(|e| DataError::IoError(format!("Impossibile creare le directory per il percorso {}: {}", path.display(), e)))?;
        }

        // Scrivi i dati
        let mut file = fs::File::create(&path)
            .map_err(|e| DataError::IoError(format!("Impossibile creare il file {}: {}", path.display(), e)))?;

        file.write_all(data)
            .map_err(|e| DataError::IoError(format!("Impossibile scrivere i dati nel file {}: {}", path.display(), e)))?;

        Ok(())
    }

    async fn load(&self, key: &str) -> DataResult<Vec<u8>> {
        let path = self.key_to_path(key);

        if !path.exists() {
            return Err(DataError::FileNotFound(format!("File non trovato: {}", path.display())));
        }

        let mut file = fs::File::open(&path)
            .map_err(|e| DataError::IoError(format!("Impossibile aprire il file {}: {}", path.display(), e)))?;

        let mut data = Vec::new();
        file.read_to_end(&mut data)
            .map_err(|e| DataError::IoError(format!("Impossibile leggere i dati dal file {}: {}", path.display(), e)))?;

        Ok(data)
    }

    async fn exists(&self, key: &str) -> DataResult<bool> {
        let path = self.key_to_path(key);
        Ok(path.exists())
    }

    async fn delete(&self, key: &str) -> DataResult<()> {
        let path = self.key_to_path(key);

        if path.exists() {
            fs::remove_file(&path)
                .map_err(|e| DataError::IoError(format!("Impossibile eliminare il file {}: {}", path.display(), e)))?;
        }

        Ok(())
    }

    async fn list_keys(&self, prefix: &str) -> DataResult<Vec<String>> {
        let mut keys = Vec::new();

        // Verifica che la directory di base esista
        if !self.base_dir.exists() {
            return Ok(keys);
        }

        // Usa walkdir per visitare ricorsivamente tutti i file
        for entry in walkdir::WalkDir::new(&self.base_dir)
            .into_iter()
            .filter_map(Result::ok)
            .filter(|e| e.file_type().is_file())
        {
            if let Ok(path) = entry.path().strip_prefix(&self.base_dir) {
                if let Some(path_str) = path.to_str() {
                    let key = path_str.replace("_", "/");
                    if key.starts_with(prefix) {
                        keys.push(key);
                    }
                }
            }
        }

        Ok(keys)
    }
}

/// Metadati di un elemento nello storage
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageItemMetadata {
    /// Nome dell'elemento
    pub name: String,

    /// Tipo di contenuto
    pub content_type: String,

    /// Data di creazione
    pub created_at: DateTime<Utc>,

    /// Data di ultima modifica
    pub modified_at: DateTime<Utc>,

    /// Dimensione in byte
    pub size: usize,

    /// Tag associati all'elemento
    pub tags: Vec<String>,
}

/// Elemento nello storage
#[derive(Debug, Clone)]
pub struct StorageItem {
    /// Chiave dell'elemento
    pub key: String,

    /// Dati dell'elemento
    pub data: Vec<u8>,

    /// Metadati dell'elemento
    pub metadata: StorageItemMetadata,
}

/// Gestore dello storage
#[derive(Clone)]
pub struct StorageManager {
    config: DataConfig,
    backend: FileSystemBackend,
}

impl StorageManager {
    /// Crea un nuovo gestore dello storage
    pub fn new(config: &DataConfig) -> DataResult<Self> {
        let storage_dir = config.base_dir.join("storage");

        // Crea la directory dello storage se non esiste
        fs::create_dir_all(&storage_dir)
            .map_err(|e| DataError::IoError(format!("Impossibile creare la directory dello storage: {}", e)))?;

        let backend = FileSystemBackend::new(storage_dir);

        Ok(Self {
            config: config.clone(),
            backend,
        })
    }

    /// Salva un elemento nello storage
    pub async fn save_item(&self, key: &str, data: &[u8], content_type: &str) -> DataResult<()> {
        // Prepara i metadati
        let now = Utc::now();
        let metadata = StorageItemMetadata {
            name: key.split('/').last().unwrap_or(key).to_string(),
            content_type: content_type.to_string(),
            created_at: now,
            modified_at: now,
            size: data.len(),
            tags: Vec::new(),
        };

        // Salva i dati
        self.backend.save(key, data).await?;

        // Salva i metadati
        let metadata_key = format!("{}{}", key, ".metadata");
        let metadata_json = serde_json::to_vec(&metadata)
            .map_err(|e| DataError::SerializationError(format!("Impossibile serializzare i metadati: {}", e)))?;

        self.backend.save(&metadata_key, &metadata_json).await?;

        Ok(())
    }

    /// Carica un elemento dallo storage
    pub async fn load_item(&self, key: &str) -> DataResult<StorageItem> {
        // Carica i dati
        let data = self.backend.load(key).await?;

        // Carica i metadati
        let metadata_key = format!("{}{}", key, ".metadata");
        let metadata = if self.backend.exists(&metadata_key).await? {
            let metadata_json = self.backend.load(&metadata_key).await?;
            serde_json::from_slice(&metadata_json)
                .map_err(|e| DataError::SerializationError(format!("Impossibile deserializzare i metadati: {}", e)))?
        } else {
            // Metadati di default se non esistono
            StorageItemMetadata {
                name: key.split('/').last().unwrap_or(key).to_string(),
                content_type: "application/octet-stream".to_string(),
                created_at: Utc::now(),
                modified_at: Utc::now(),
                size: data.len(),
                tags: Vec::new(),
            }
        };

        Ok(StorageItem {
            key: key.to_string(),
            data,
            metadata,
        })
    }

    /// Verifica se un elemento esiste nello storage
    pub async fn item_exists(&self, key: &str) -> DataResult<bool> {
        self.backend.exists(key).await
    }

    /// Elimina un elemento dallo storage
    pub async fn delete_item(&self, key: &str) -> DataResult<()> {
        // Elimina i dati
        self.backend.delete(key).await?;

        // Elimina i metadati
        let metadata_key = format!("{}{}", key, ".metadata");
        if self.backend.exists(&metadata_key).await? {
            self.backend.delete(&metadata_key).await?;
        }

        Ok(())
    }

    /// Elenca le chiavi con un prefisso
    pub async fn list_keys(&self, prefix: &str) -> DataResult<Vec<String>> {
        self.backend.list_keys(prefix).await
    }

    /// Aggiorna i metadati di un elemento
    pub async fn update_metadata(&self, key: &str, metadata: StorageItemMetadata) -> DataResult<()> {
        let metadata_key = format!("{}{}", key, ".metadata");
        let metadata_json = serde_json::to_vec(&metadata)
            .map_err(|e| DataError::SerializationError(format!("Impossibile serializzare i metadati: {}", e)))?;

        self.backend.save(&metadata_key, &metadata_json).await?;

        Ok(())
    }

    /// Carica solo i metadati di un elemento
    pub async fn load_metadata(&self, key: &str) -> DataResult<StorageItemMetadata> {
        let metadata_key = format!("{}{}", key, ".metadata");

        if !self.backend.exists(&metadata_key).await? {
            return Err(DataError::FileNotFound(format!("Metadati non trovati per la chiave: {}", key)));
        }

        let metadata_json = self.backend.load(&metadata_key).await?;
        let metadata = serde_json::from_slice(&metadata_json)
            .map_err(|e| DataError::SerializationError(format!("Impossibile deserializzare i metadati: {}", e)))?;

        Ok(metadata)
    }
}
