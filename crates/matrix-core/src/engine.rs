//! Core Engine centrale di MATRIX IDE
//! 
//! Questo modulo contiene l'implementazione del Core Engine, che coordina
//! tutti i componenti principali di MATRIX IDE.

use crate::{
    event_bus::{EventBus, Event},
    state::GlobalState,
    plugin::PluginManager,
    error::CoreError,
};
use std::sync::{Arc, RwLock};
use tokio::runtime::Runtime;
use crate::micro_task_fabric::{MicroTaskFabric, MicroTaskFabricConfig, TaskContext, ProgrammingLanguage};
use crate::chain_reaction_engine::ChainReactionEngine;

/// Struttura principale del Core Engine
pub struct Engine {
    /// Bus degli eventi condiviso tra tutti i componenti
    event_bus: Arc<EventBus>,

    /// Stato globale dell'applicazione
    state: Arc<RwLock<GlobalState>>,

    /// Gestore dei plugin
    plugin_manager: Arc<PluginManager>,

    /// Runtime Tokio per operazioni asincrone
    runtime: Runtime,

    /// Micro-Task Fabric per l'orchestrazione delle attività
    micro_task_fabric: Arc<MicroTaskFabric>,

    /// Chain Reaction Engine per l'analisi degli impatti
    chain_reaction_engine: Arc<ChainReactionEngine>,
}

impl Engine {
    /// Getter pubblico per accedere al micro_task_fabric (necessario per API e test)
    pub fn micro_task_fabric(&self) -> &Arc<MicroTaskFabric> {
        &self.micro_task_fabric
    }

    /// Getter pubblico per accedere al chain_reaction_engine
    pub fn chain_reaction_engine(&self) -> &Arc<ChainReactionEngine> {
        &self.chain_reaction_engine
    }

    /// Crea una nuova istanza del Core Engine con configurazione predefinita
    pub fn new() -> Result<Self, CoreError> {
        let runtime = tokio::runtime::Builder::new_multi_thread()
            .enable_all()
            .build()
            .map_err(|e| CoreError::InitializationError(format!("Failed to create Tokio runtime: {}", e)))?;

        let event_bus = Arc::new(EventBus::new());
        let state = Arc::new(RwLock::new(GlobalState::new()));
        let plugin_manager = Arc::new(PluginManager::new(event_bus.clone(), state.clone()));

        // Crea chain reaction engine
        let chain_reaction_engine = Arc::new(ChainReactionEngine::new_simple());

        // Crea micro task fabric
        let micro_task_fabric = Arc::new(MicroTaskFabric::new(
            event_bus.clone(),
            Arc::new(()),
            chain_reaction_engine.clone(),
            MicroTaskFabricConfig::default(),
        ));

        Ok(Self {
            event_bus,
            state,
            plugin_manager,
            runtime,
            micro_task_fabric,
            chain_reaction_engine,
        })
    }

    /// Avvia il Core Engine
    pub fn start(&self) -> Result<(), CoreError> {
        // Inizializza i componenti
        self.event_bus.start()?;

        // Carica i plugin
        self.plugin_manager.load_plugins()?;

        // Emette l'evento di avvio
        self.event_bus.emit(Event::SystemStarted)?;

        Ok(())
    }

    /// Arresta il Core Engine
    pub fn stop(&self) -> Result<(), CoreError> {
        // Emette l'evento di arresto
        self.event_bus.emit(Event::SystemStopping)?;

        // Scarica i plugin
        self.plugin_manager.unload_plugins()?;

        // Arresta i componenti
        self.event_bus.stop()?;

        Ok(())
    }

    /// Ottiene un riferimento all'Event Bus
    pub fn event_bus(&self) -> Arc<EventBus> {
        self.event_bus.clone()
    }

    /// Ottiene un riferimento allo stato globale
    pub fn state(&self) -> Arc<RwLock<GlobalState>> {
        self.state.clone()
    }

    /// Ottiene un riferimento al gestore dei plugin
    pub fn plugin_manager(&self) -> Arc<PluginManager> {
        self.plugin_manager.clone()
    }

    /// Esegue un task asincrono nel runtime Tokio
    pub fn spawn<F>(&self, future: F)
    where
        F: std::future::Future<Output = ()> + Send + 'static,
    {
        self.runtime.spawn(future);
    }
    /// Esegue l'orchestrazione di un prompt a MVP
    pub async fn execute_prompt_to_mvp(&self, prompt: &str, project_root: &str) -> Result<String, CoreError> {
        let context = TaskContext {
            project_root: project_root.to_string(),
            current_file: None,
            cursor_position: None,
            selected_text: None,
            open_files: vec![],
            git_branch: Some("main".to_string()),
            language: ProgrammingLanguage::Rust,
        };

        match self.micro_task_fabric.orchestrate_prompt_to_mvp(prompt, context).await {
            Ok(result) => Ok(format!("MVP Result: {:?}", result)),
            Err(e) => Err(CoreError::Custom(format!("Orchestration failed: {}", e)))
        }
    }
}

impl Drop for Engine {
    fn drop(&mut self) {
        // Assicura che tutti i componenti vengano arrestati correttamente
        if let Err(e) = self.stop() {
            eprintln!("Error stopping engine: {}", e);
        }
    }
}