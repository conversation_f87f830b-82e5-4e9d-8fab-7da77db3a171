//! Gestione degli errori per il Core Engine di MATRIX IDE
//!
//! Questo modulo definisce i tipi di errore specifici del Core Engine.

use thiserror::Error;
use std::io;

/// Errori che possono verificarsi nel Core Engine
#[derive(Error, Debug)]
pub enum CoreError {
    /// Errore durante l'inizializzazione del Core Engine
    #[error("Initialization error: {0}")]
    InitializationError(String),

    /// Errore nell'Event Bus
    #[error("Event bus error: {0}")]
    EventBusError(String),

    /// Errore nel sistema di plugin
    #[error("Plugin error: {0}")]
    PluginError(String),

    /// Errore di I/O
    #[error("I/O error: {0}")]
    IoError(#[from] io::Error),

    /// Errore di serializzazione
    #[error("Serialization error: {0}")]
    SerializationError(String),

    /// Errore di deserializzazione
    #[error("Deserialization error: {0}")]
    DeserializationError(String),

    /// Errore di lock
    #[error("Lock error: {0}")]
    LockError(String),

    /// Errore generico personalizzato
    #[error("Custom error: {0}")]
    Custom(String),

    /// Tipo di task non valido
    #[error("Invalid task type: {0}")]
    InvalidTaskType(String),

    /// Parametro mancante
    #[error("Missing parameter: {0}")]
    MissingParameter(String),

    /// Errore generico
    #[error("Error: {0}")]
    GenericError(String),

    /// Errore nel DAG Engine
    #[error("DAG error: {0}")]
    DagError(String),
}

impl From<serde_json::Error> for CoreError {
    fn from(err: serde_json::Error) -> Self {
        CoreError::SerializationError(err.to_string())
    }
}

impl From<String> for CoreError {
    fn from(err: String) -> Self {
        CoreError::GenericError(err)
    }
}

impl From<&str> for CoreError {
    fn from(err: &str) -> Self {
        CoreError::GenericError(err.to_string())
    }
}
