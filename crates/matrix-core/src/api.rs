//! API interne per l’orchestratore MATRIX Core

use crate::{
    engine::Engine,
    dag_engine::{DagEngine, Task, TaskState},
    micro_task_fabric::{MicroTask, MVPResult, TaskContext},
    error::CoreError,
};
use std::sync::Arc;

/// Trait per le API di orchestrazione
pub trait OrchestratorAPI {
    /// Avvia una nuova orchestrazione a partire da un prompt
    fn start_orchestration(&self, prompt: &str, context: TaskContext) -> Result<MVPResult, CoreError>;

    /// Restituisce lo stato di un task dato il suo ID
    fn get_task_state(&self, task_id: &str) -> Result<TaskState, CoreError>;

    /// Restituisce il risultato di un task dato il suo ID
    fn get_task_result(&self, task_id: &str) -> Result<Option<serde_json::Value>, CoreError>;
}

/// Implementazione base dell’API su Engine
pub struct MatrixOrchestrator {
    engine: Arc<Engine>,
}

impl MatrixOrchestrator {
    pub fn new(engine: Arc<Engine>) -> Self {
        Self { engine }
    }
}

impl OrchestratorAPI for MatrixOrchestrator {
    fn start_orchestration(&self, prompt: &str, context: TaskContext) -> Result<MVPResult, CoreError> {
        // Esegue orchestrazione asincrona (semplificata per MVP)
        // Usa runtime Tokio per wrapper sincrono
        let runtime = tokio::runtime::Runtime::new().map_err(|e| {
            CoreError::Custom(format!("Failed to create runtime: {}", e))
        })?;

        let result = runtime.block_on(async {
            self.engine.execute_prompt_to_mvp(prompt, &context.project_root).await
        })?;

        // Converte il risultato stringa in MVPResult
        Ok(MVPResult::Success {
            generated_files: vec![],
            execution_summary: result,
        })
    }

    /// Restituisce lo stato di un task dato il suo ID (sincrono, MVP)
    fn get_task_state(&self, task_id: &str) -> Result<TaskState, CoreError> {
        use uuid::Uuid;
        let uuid = Uuid::parse_str(task_id).map_err(|e| CoreError::from(e.to_string()))?;
        // Accesso diretto alla mappa dei task (da rendere async in futuro)
        let tasks = self.engine.micro_task_fabric().tasks().read().map_err(|e| {
            CoreError::LockError(format!("Failed to acquire read lock: {}", e))
        })?;
        let task = tasks.get(&uuid).ok_or_else(|| CoreError::from("Task non trovato"))?;
        Ok(match task.status {
            crate::micro_task_fabric::TaskStatus::Created => TaskState::Pending, // Mapping corretto: Created -> Pending
            crate::micro_task_fabric::TaskStatus::Queued => TaskState::Pending,  // Mapping: Queued -> Pending
            crate::micro_task_fabric::TaskStatus::Running => TaskState::Running,
            crate::micro_task_fabric::TaskStatus::Completed => TaskState::Completed,
            crate::micro_task_fabric::TaskStatus::Failed { .. } => TaskState::Failed,
            crate::micro_task_fabric::TaskStatus::Cancelled => TaskState::Cancelled,
            crate::micro_task_fabric::TaskStatus::RolledBack => TaskState::RolledBack,
        })
    }

    /// Restituisce il risultato di un task dato il suo ID (sincrono, MVP)
    fn get_task_result(&self, task_id: &str) -> Result<Option<serde_json::Value>, CoreError> {
        use uuid::Uuid;
        let uuid = Uuid::parse_str(task_id).map_err(|e| CoreError::from(e.to_string()))?;
        let tasks = self.engine.micro_task_fabric().tasks().read().map_err(|e| {
            CoreError::LockError(format!("Failed to acquire read lock: {}", e))
        })?;
        let task = tasks.get(&uuid).ok_or_else(|| CoreError::from("Task non trovato"))?;
        if let Some(output) = &task.output {
            Ok(Some(serde_json::to_value(&output.result).map_err(|e| CoreError::from(e.to_string()))?))
        } else {
            Ok(None)
        }
    }
}