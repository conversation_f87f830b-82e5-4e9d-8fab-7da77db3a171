//! Sistema di Event Bus per MATRIX IDE
//!
//! Questo modulo implementa un sistema di eventi che permette la comunicazione
//! tra componenti diversi dell'IDE in modo disaccoppiato.

use crate::error::CoreError;
use std::sync::{Arc, RwLock};
use std::collections::HashMap;
use uuid::Uuid;
use serde::{Serialize, Deserialize};
use async_trait::async_trait;
use tokio::sync::mpsc::{self, Sender, Receiver};
use tokio::task::JoinHandle;
use std::any::Any;

use tracing::error;
 
/// Rappresenta un evento nel sistema
#[derive(Clone, Debug, Serialize, Deserialize)]
/// Rappresenta un evento generato o gestito dal sistema MATRIX IDE.
///
/// Gli eventi permettono la comunicazione asincrona e disaccoppiata tra i vari componenti.
/// È possibile estendere questa enum per supportare nuovi tipi di eventi.
pub enum Event {
    /// Evento di sistema: avvio completato
    SystemStarted,

    /// Evento di sistema: arresto in corso
    SystemStopping,

    /// Evento di file: file aperto
    FileOpened { path: String },

    /// Evento di file: file salvato
    FileSaved { path: String },

    /// Evento di file: file chiuso
    FileClosed { path: String },

    /// Evento di editor: modifica del testo
    TextEdited { 
        file_id: String, 
        change: String,
        position: usize,
    },

    /// Evento di plugin: plugin caricato
    PluginLoaded { id: String, name: String },

    /// Evento di plugin: plugin scaricato
    PluginUnloaded { id: String },

    /// Evento personalizzato definito dai plugin
    Custom { 
        source: String,
        name: String,
        data: serde_json::Value,
    },
    
    // Nuovi eventi per Micro-Task Fabric
    MicroTaskCreated {
        task_id: String,
        task_type: String,
        priority: u8
    },
    MicroTaskStarted {
        task_id: String,
        estimated_duration_secs: u64
    },
    MicroTaskCompleted {
        task_id: String,
        result: String,
        execution_time_ms: u64
    },
    MicroTaskFailed {
        task_id: String,
        error: String,
        rollback_initiated: bool
    },

    // Eventi Chain Reaction
    ChainReactionDetected {
        change_description: String,
        impact_level: String,
        affected_components: Vec<String>
    },
    DependencyConflict {
        package_name: String,
        conflict_type: String,
        suggested_resolution: String
    },
    AlternativeGenerated {
        original_task_id: String,
        alternatives: Vec<String>
    },

    // Eventi DAG Execution
    DAGExecutionStarted {
        total_tasks: usize,
        estimated_total_time_secs: u64
    },
    DAGExecutionCompleted {
        completed_tasks: usize,
        failed_tasks: usize,
        total_time_ms: u64
    },

    // Eventi UI Plugin
    PluginViewCreated {
        plugin_id: String,
        view_id: String,
        panel_type: String,
    },
    PluginViewDestroyed {
        plugin_id: String,
        view_id: String,
    },
    PluginViewShown {
        plugin_id: String,
        view_id: String,
    },
    PluginViewHidden {
        plugin_id: String,
        view_id: String,
    },
    PluginCommand {
        plugin_id: String,
        command: String,
        args: serde_json::Value,
    },

    // Eventi Log Agente
    AgentLog {
        level: crate::log_level::LogLevel,
        message: String,
        context: Option<serde_json::Value>,
        timestamp: u64,
    },
}

impl Event {
    /// Crea un nuovo evento personalizzato
    pub fn custom<T: Serialize>(source: &str, name: &str, data: &T) -> Result<Self, CoreError> {
        let data = serde_json::to_value(data)
            .map_err(|e| CoreError::SerializationError(format!("Failed to serialize event data: {}", e)))?;

        Ok(Event::Custom {
            source: source.to_string(),
            name: name.to_string(),
            data,
        })
    }

    /// Ottiene il tipo di evento come stringa
    pub fn event_type(&self) -> &'static str {
        match self {
            Event::SystemStarted => "system.started",
            Event::SystemStopping => "system.stopping",
            Event::FileOpened { .. } => "file.opened",
            Event::FileSaved { .. } => "file.saved",
            Event::FileClosed { .. } => "file.closed",
            Event::TextEdited { .. } => "editor.text_edited",
            Event::PluginLoaded { .. } => "plugin.loaded",
            Event::PluginUnloaded { .. } => "plugin.unloaded",
            Event::Custom { .. } => "custom",
            Event::MicroTaskCreated { .. } => "microtask.created",
            Event::MicroTaskStarted { .. } => "microtask.started",
            Event::MicroTaskCompleted { .. } => "microtask.completed",
            Event::MicroTaskFailed { .. } => "microtask.failed",
            Event::ChainReactionDetected { .. } => "chainreaction.detected",
            Event::DependencyConflict { .. } => "dependency.conflict",
            Event::AlternativeGenerated { .. } => "alternative.generated",
            Event::DAGExecutionStarted { .. } => "dag.started",
            Event::DAGExecutionCompleted { .. } => "dag.completed",
            Event::PluginViewCreated { .. } => "plugin.view.created",
            Event::PluginViewDestroyed { .. } => "plugin.view.destroyed",
            Event::PluginViewShown { .. } => "plugin.view.shown",
            Event::PluginViewHidden { .. } => "plugin.view.hidden",
            Event::PluginCommand { .. } => "plugin.command",
            Event::AgentLog { .. } => "agent.log",
        }
    }
}

/// Trait per gli handler degli eventi
#[async_trait]
/// Trait che rappresenta un gestore di eventi per il Core Event Bus.
///
/// Implementa questo trait per ricevere notifiche sugli eventi a cui sei iscritto.
pub trait EventHandler: Send + Sync {
    /// Gestisce un evento ricevuto dal bus.
    ///
    /// # Parametri
    /// * `event` - L'evento da gestire.
    ///
    /// # Ritorna
    /// * `Result<(), CoreError>` - Ok se gestito correttamente, Err in caso di errore.
    async fn handle(&self, event: Event) -> Result<(), CoreError>;
}

/// Subscriber agli eventi
/// Rappresenta un subscriber (ascoltatore) registrato all'Event Bus.
///
/// Ogni subscriber specifica i tipi di eventi di interesse e fornisce un handler.
pub struct EventSubscriber {
    /// ID univoco del subscriber
    id: Uuid,

    /// Tipi di eventi a cui il subscriber è interessato
    event_types: Vec<String>,

    /// Handler degli eventi
    handler: Arc<dyn EventHandler>,
}

impl EventSubscriber {
    /// Crea un nuovo subscriber agli eventi.
    ///
    /// # Parametri
    /// * `event_types` - Iterator di tipi di eventi a cui iscriversi (es. "file.opened", "*").
    /// * `handler` - Handler che implementa [`EventHandler`] per la gestione degli eventi.
    pub fn new<S: AsRef<str>, I: IntoIterator<Item = S>>(
        event_types: I,
        handler: Arc<dyn EventHandler>,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            event_types: event_types.into_iter().map(|s| s.as_ref().to_string()).collect(),
            handler,
        }
    }

    /// Restituisce l'ID univoco del subscriber.
    pub fn id(&self) -> Uuid {
        self.id
    }

    /// Verifica se il subscriber è interessato a un dato tipo di evento.
    ///
    /// # Parametri
    /// * `event_type` - Tipo di evento da verificare.
    pub fn is_interested_in(&self, event_type: &str) -> bool {
        self.event_types.iter().any(|t| t == event_type || t == "*")
    }

    /// Inoltra un evento al proprio handler.
    ///
    /// # Parametri
    /// * `event` - Evento da gestire.
    pub async fn handle_event(&self, event: Event) -> Result<(), CoreError> {
        self.handler.handle(event).await
    }
}

/// Bus degli eventi
/// Core Event Bus per la gestione e la distribuzione degli eventi tra i componenti.
///
/// Permette la sottoscrizione, l'emissione e la gestione asincrona degli eventi.
pub struct EventBus {
    /// Subscribers agli eventi
    subscribers: RwLock<HashMap<Uuid, Arc<EventSubscriber>>>,

    /// Canale di invio degli eventi
    event_sender: RwLock<Option<Sender<Event>>>,

    /// Handle del task di processing degli eventi
    processing_task: RwLock<Option<JoinHandle<()>>>,
}

impl EventBus {
    /// Crea una nuova istanza di [`EventBus`].
    pub fn new() -> Self {
        Self {
            subscribers: RwLock::new(HashMap::new()),
            event_sender: RwLock::new(None),
            processing_task: RwLock::new(None),
        }
    }

    /// Avvia l'Event Bus
    pub fn start(&self) -> Result<(), CoreError> {
        let (tx, rx) = mpsc::channel::<Event>(100);

        {
            let mut sender = self.event_sender.write().map_err(|_| {
                CoreError::LockError("Failed to acquire write lock on event sender".to_string())
            })?;
            *sender = Some(tx);
        }

        let subscribers = self.subscribers.read().unwrap().clone();
        let task = tokio::spawn(async move {
            Self::process_events(rx, subscribers).await;
        });

        {
            let mut processing_task = self.processing_task.write().map_err(|_| {
                CoreError::LockError("Failed to acquire write lock on processing task".to_string())
            })?;
            *processing_task = Some(task);
        }

        Ok(())
    }

    /// Arresta l'Event Bus
    pub fn stop(&self) -> Result<(), CoreError> {
        // Chiude il canale degli eventi
        {
            let mut sender = self.event_sender.write().map_err(|_| {
                CoreError::LockError("Failed to acquire write lock on event sender".to_string())
            })?;
            *sender = None;
        }

        // Attende il completamento del task di processing
        {
            let mut processing_task = self.processing_task.write().map_err(|_| {
                CoreError::LockError("Failed to acquire write lock on processing task".to_string())
            })?;
            if let Some(task) = processing_task.take() {
                // Non possiamo usare .await qui perché non siamo in un contesto asincrono
                // ma possiamo fare abort per terminare immediatamente
                task.abort();
            }
        }

        Ok(())
    }

    /// Aggiunge un subscriber all'Event Bus
    pub fn subscribe(&self, subscriber: Arc<EventSubscriber>) -> Result<Uuid, CoreError> {
        let id = subscriber.id();

        let mut subscribers = self.subscribers.write().map_err(|_| {
            CoreError::LockError("Failed to acquire write lock on subscribers".to_string())
        })?;

        subscribers.insert(id, subscriber);

        Ok(id)
    }

    /// Rimuove un subscriber dall'Event Bus
    pub fn unsubscribe(&self, id: Uuid) -> Result<(), CoreError> {
        let mut subscribers = self.subscribers.write().map_err(|_| {
            CoreError::LockError("Failed to acquire write lock on subscribers".to_string())
        })?;

        subscribers.remove(&id);

        Ok(())
    }

    /// Emette un evento
    pub fn emit(&self, event: Event) -> Result<(), CoreError> {
        let sender = self.event_sender.read().map_err(|_| {
            CoreError::LockError("Failed to acquire read lock on event sender".to_string())
        })?;

        if let Some(sender) = &*sender {
            sender.try_send(event.clone()).map_err(|e| {
                CoreError::EventBusError(format!("Failed to send event: {}", e))
            })?;
        } else {
            return Err(CoreError::EventBusError("Event bus not started".to_string()));
        }

        Ok(())
    }

    /// Elabora gli eventi in arrivo
    async fn process_events(mut rx: Receiver<Event>, subscribers: RwLock<HashMap<Uuid, Arc<EventSubscriber>>>) {
        while let Some(event) = rx.recv().await {
            let event_type = event.event_type().to_string();

            // Ottiene i subscriber interessati all'evento
            let interested_subscribers = {
                let subscribers = match subscribers.read() {
                    Ok(guard) => guard,
                    Err(_) => {
                        error!("Failed to acquire read lock on subscribers");
                        continue;
                    }
                };

                subscribers
                    .values()
                    .filter(|s| s.is_interested_in(&event_type))
                    .cloned()
                    .collect::<Vec<_>>()
            };

            // Invia l'evento ai subscriber interessati
            for subscriber in interested_subscribers {
                let event_clone = event.clone();
                tokio::spawn(async move {
                    if let Err(e) = subscriber.handle_event(event_clone).await {
                        error!("Error handling event: {}", e);
                    }
                });
            }
        }
    }
}