//! Implementazione del DAG Engine per MATRIX IDE
//!
//! Questo modulo fornisce un'implementazione del Directed Acyclic Graph (DAG) Engine
//! che consente l'esecuzione di pipeline di micro-task con dipendenze.

use crate::error::CoreError;
use crate::event_bus::{EventBus, Event};
use std::collections::{HashMap, HashSet, VecDeque};
use std::sync::{Arc, RwLock};
use async_trait::async_trait;
use serde::{Serialize, Deserialize};
use uuid::Uuid;

/// Stato di esecuzione di un task
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum TaskState {
    /// Task non ancora eseguito
    Pending,

    /// Task in esecuzione
    Running,

    /// Task completato con successo
    Completed,

    /// Task fallito
    Failed,

    /// Task annullato
    Cancelled,

    /// Task rollback eseguito
    RolledBack,
}

/// Metadati di un task
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TaskMetadata {
    /// Nome del task
    pub name: String,

    /// Descrizione del task
    pub description: String,

    /// Tipo di task (es. "code_generation", "code_analysis", "refactoring")
    pub task_type: String,

    /// Timestamp di creazione
    pub created_at: chrono::DateTime<chrono::Utc>,

    /// Timestamp di inizio esecuzione
    pub started_at: Option<chrono::DateTime<chrono::Utc>>,

    /// Timestamp di completamento
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,

    /// Priorità del task (valore più alto = priorità maggiore)
    pub priority: u32,

    /// Tempo stimato di esecuzione in millisecondi
    pub estimated_duration_ms: Option<u64>,

    /// Parametri aggiuntivi del task
    pub parameters: serde_json::Value,
}

/// Rappresenta un task all'interno del DAG
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Task {
    /// ID univoco del task
    pub id: String,

    /// Metadati del task
    pub metadata: TaskMetadata,

    /// Stato corrente del task
    pub state: TaskState,

    /// Risultato del task (opzionale)
    pub result: Option<serde_json::Value>,

    /// Messaggio di errore (se il task è fallito)
    pub error_message: Option<String>,

    /// IDs dei task da cui questo task dipende
    pub dependencies: Vec<String>,
}

impl Task {
    /// Crea un nuovo task
    pub fn new(
        name: impl Into<String>,
        description: impl Into<String>,
        task_type: impl Into<String>,
        dependencies: Vec<String>,
        parameters: serde_json::Value,
    ) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            metadata: TaskMetadata {
                name: name.into(),
                description: description.into(),
                task_type: task_type.into(),
                created_at: chrono::Utc::now(),
                started_at: None,
                completed_at: None,
                priority: 0,
                estimated_duration_ms: None,
                parameters,
            },
            state: TaskState::Pending,
            result: None,
            error_message: None,
            dependencies,
        }
    }

    /// Imposta la priorità del task
    pub fn with_priority(mut self, priority: u32) -> Self {
        self.metadata.priority = priority;
        self
    }

    /// Imposta la durata stimata del task
    pub fn with_estimated_duration(mut self, duration_ms: u64) -> Self {
        self.metadata.estimated_duration_ms = Some(duration_ms);
        self
    }
}

/// Trait per eseguire un task
#[async_trait]
pub trait TaskExecutor: Send + Sync {
    /// Esegue un task e restituisce il risultato
    async fn execute(&self, task: &Task) -> Result<serde_json::Value, CoreError>;
}

/// Rappresenta un grafo aciclico diretto (DAG) di task
pub struct DagGraph {
    /// Mappa degli ID dei task ai task
    tasks: HashMap<String, Task>,

    /// Mappa degli ID dei task alle dipendenze inverse (task che dipendono da questo task)
    reverse_dependencies: HashMap<String, Vec<String>>,
}

impl DagGraph {
    /// Crea un nuovo grafo DAG vuoto
    pub fn new() -> Self {
        Self {
            tasks: HashMap::new(),
            reverse_dependencies: HashMap::new(),
        }
    }

    /// Aggiunge un task al grafo
    pub fn add_task(&mut self, task: Task) -> Result<(), CoreError> {
        // Verifica che il task non esista già
        if self.tasks.contains_key(&task.id) {
            return Err(CoreError::DagError(format!("Task with ID {} already exists", task.id)));
        }

        // Verifica che tutte le dipendenze esistano
        for dep_id in &task.dependencies {
            if !self.tasks.contains_key(dep_id) {
                return Err(CoreError::DagError(format!("Dependency with ID {} does not exist", dep_id)));
            }
        }

        // Aggiorna le dipendenze inverse
        for dep_id in &task.dependencies {
            self.reverse_dependencies
                .entry(dep_id.clone())
                .or_insert_with(Vec::new)
                .push(task.id.clone());
        }

        // Aggiungi il task alla mappa
        let task_id = task.id.clone();
        self.tasks.insert(task_id.clone(), task);

        // Inizializza la entry per le dipendenze inverse se non esiste
        self.reverse_dependencies.entry(task_id).or_insert_with(Vec::new);

        Ok(())
    }

    /// Ottiene un task dal grafo
    pub fn get_task(&self, task_id: &str) -> Option<&Task> {
        self.tasks.get(task_id)
    }

    /// Ottiene un task mutabile dal grafo
    pub fn get_task_mut(&mut self, task_id: &str) -> Option<&mut Task> {
        self.tasks.get_mut(task_id)
    }

    /// Verifica se un task è pronto per essere eseguito
    pub fn is_task_ready(&self, task_id: &str) -> bool {
        let task = match self.get_task(task_id) {
            Some(t) => t,
            None => return false,
        };

        // Se il task non è in stato Pending, non è pronto
        if task.state != TaskState::Pending {
            return false;
        }

        // Verifica che tutte le dipendenze siano completate
        for dep_id in &task.dependencies {
            if let Some(dep) = self.get_task(dep_id) {
                if dep.state != TaskState::Completed {
                    return false;
                }
            } else {
                return false;
            }
        }

        true
    }

    /// Ottiene tutti i task pronti per l'esecuzione
    pub fn get_ready_tasks(&self) -> Vec<String> {
        self.tasks
            .keys()
            .filter(|id| self.is_task_ready(id))
            .cloned()
            .collect()
    }

    /// Verifica la presenza di cicli nel grafo
    pub fn check_cycles(&self) -> Result<(), CoreError> {
        // Set di task visitati nell'attraversamento corrente
        let mut visited = HashSet::new();
        // Set di task completamente esplorati
        let mut explored = HashSet::new();

        // Funzione ricorsiva per l'attraversamento DFS
        fn dfs(
            graph: &DagGraph,
            task_id: &str,
            visited: &mut HashSet<String>,
            explored: &mut HashSet<String>,
        ) -> Result<(), CoreError> {
            // Se il nodo è già stato visitato in questo attraversamento, abbiamo un ciclo
            if visited.contains(task_id) {
                return Err(CoreError::DagError(format!("Cycle detected involving task {}", task_id)));
            }

            // Se il nodo è già stato esplorato completamente, possiamo saltarlo
            if explored.contains(task_id) {
                return Ok(());
            }

            // Marca il nodo come visitato per questo attraversamento
            visited.insert(task_id.to_string());

            // Visita tutte le dipendenze inverse (task che dipendono da questo task)
            if let Some(deps) = graph.reverse_dependencies.get(task_id) {
                for dep_id in deps {
                    dfs(graph, dep_id, visited, explored)?;
                }
            }

            // Rimuovi il nodo dai visitati e aggiungilo agli esplorati
            visited.remove(task_id);
            explored.insert(task_id.to_string());

            Ok(())
        }

        // Avvia l'attraversamento da ogni nodo non esplorato
        for task_id in self.tasks.keys() {
            if !explored.contains(task_id.as_str()) {
                dfs(self, task_id, &mut visited, &mut explored)?;
            }
        }

        Ok(())
    }

    /// Calcola un ordinamento topologico dei task
    pub fn topological_sort(&self) -> Result<Vec<String>, CoreError> {
        // Verifica che non ci siano cicli
        self.check_cycles()?;

        let mut result = Vec::new();
        let mut in_degree = HashMap::new();
        let mut queue = VecDeque::new();

        // Calcola il grado entrante per ogni nodo
        for task_id in self.tasks.keys() {
            in_degree.insert(task_id.clone(), 0);
        }

        // Aggiorna il grado entrante in base alle dipendenze
        for task in self.tasks.values() {
            for dep_id in &task.dependencies {
                *in_degree.entry(dep_id.clone()).or_insert(0) += 1;
            }
        }

        // Aggiungi alla coda tutti i nodi con grado entrante zero
        for (task_id, degree) in &in_degree {
            if *degree == 0 {
                queue.push_back(task_id.clone());
            }
        }

        // Elabora la coda
        while let Some(task_id) = queue.pop_front() {
            result.push(task_id.clone());

            // Riduci il grado entrante di tutti i nodi dipendenti
            if let Some(deps) = self.reverse_dependencies.get(&task_id) {
                for dep_id in deps {
                    if let Some(degree) = in_degree.get_mut(dep_id) {
                        *degree -= 1;
                        if *degree == 0 {
                            queue.push_back(dep_id.clone());
                        }
                    }
                }
            }
        }

        // Verifica che tutti i nodi siano stati visitati
        if result.len() != self.tasks.len() {
            return Err(CoreError::DagError("Topological sort failed, the graph may contain cycles".to_string()));
        }

        Ok(result)
    }
}

/// Motore di esecuzione per i grafi DAG
pub struct DagEngine {
    /// Evento bus per notificare gli eventi
    event_bus: Arc<EventBus>,

    /// Esecutori di task registrati
    executors: RwLock<HashMap<String, Arc<dyn TaskExecutor>>>,

    /// Grafi DAG in esecuzione
    graphs: RwLock<HashMap<String, Arc<RwLock<DagGraph>>>>,
}

impl DagEngine {
    /// Crea un nuovo DAG Engine
    pub fn new(event_bus: Arc<EventBus>) -> Self {
        Self {
            event_bus,
            executors: RwLock::new(HashMap::new()),
            graphs: RwLock::new(HashMap::new()),
        }
    }

    /// Registra un esecutore di task
    pub fn register_executor(&self, task_type: impl Into<String>, executor: Arc<dyn TaskExecutor>) -> Result<(), CoreError> {
        let mut executors = self.executors.write().map_err(|_| {
            CoreError::LockError("Failed to acquire write lock on executors".to_string())
        })?;

        executors.insert(task_type.into(), executor);

        Ok(())
    }

    /// Crea un nuovo grafo DAG
    pub fn create_graph(&self, graph_id: impl Into<String>) -> Result<Arc<RwLock<DagGraph>>, CoreError> {
        let graph_id = graph_id.into();
        let mut graphs = self.graphs.write().map_err(|_| {
            CoreError::LockError("Failed to acquire write lock on graphs".to_string())
        })?;

        if graphs.contains_key(&graph_id) {
            return Err(CoreError::DagError(format!("Graph with ID {} already exists", graph_id)));
        }

        let graph = Arc::new(RwLock::new(DagGraph::new()));
        graphs.insert(graph_id, graph.clone());

        Ok(graph)
    }

    /// Ottiene un grafo DAG esistente
    pub fn get_graph(&self, graph_id: &str) -> Result<Arc<RwLock<DagGraph>>, CoreError> {
        let graphs = self.graphs.read().map_err(|_| {
            CoreError::LockError("Failed to acquire read lock on graphs".to_string())
        })?;

        graphs.get(graph_id)
            .cloned()
            .ok_or_else(|| CoreError::DagError(format!("Graph with ID {} not found", graph_id)))
    }

    /// Esegue un grafo DAG completo
    pub async fn execute_graph(&self, graph_id: &str) -> Result<(), CoreError> {
        let graph = self.get_graph(graph_id)?;

        // Ottieni l'ordinamento topologico
        let task_order = {
            let graph_guard = graph.read().map_err(|_| {
                CoreError::LockError("Failed to acquire read lock on graph".to_string())
            })?;

            graph_guard.topological_sort()?
        };

        // Esegui i task in ordine topologico
        for task_id in task_order {
            self.execute_task(graph_id, &task_id).await?;
        }

        Ok(())
    }

    /// Esegue un singolo task
    pub async fn execute_task(&self, graph_id: &str, task_id: &str) -> Result<(), CoreError> {
        let graph = self.get_graph(graph_id)?;

        // Verifica che il task sia pronto per l'esecuzione
        {
            let graph_guard = graph.read().map_err(|_| {
                CoreError::LockError("Failed to acquire read lock on graph".to_string())
            })?;

            if !graph_guard.is_task_ready(task_id) {
                return Err(CoreError::DagError(format!("Task {} is not ready for execution", task_id)));
            }
        }

        // Ottieni il task e aggiorna il suo stato a Running
        let (task, task_type) = {
            let mut graph_guard = graph.write().map_err(|_| {
                CoreError::LockError("Failed to acquire write lock on graph".to_string())
            })?;

            let task = graph_guard.get_task_mut(task_id)
                .ok_or_else(|| CoreError::DagError(format!("Task {} not found in graph {}", task_id, graph_id)))?;

            // Aggiorna lo stato e il timestamp di inizio
            task.state = TaskState::Running;
            task.metadata.started_at = Some(chrono::Utc::now());

            (task.clone(), task.metadata.task_type.clone())
        };

        // Ottieni l'esecutore appropriato
        let executor = {
            let executors = self.executors.read().map_err(|_| {
                CoreError::LockError("Failed to acquire read lock on executors".to_string())
            })?;

            executors.get(&task_type)
                .cloned()
                .ok_or_else(|| CoreError::DagError(format!("No executor registered for task type {}", task_type)))?
        };

        // Esegui il task
        let result = executor.execute(&task).await;

        // Aggiorna lo stato del task in base al risultato
        {
            let mut graph_guard = graph.write().map_err(|_| {
                CoreError::LockError("Failed to acquire write lock on graph".to_string())
            })?;

            let task = graph_guard.get_task_mut(task_id)
                .ok_or_else(|| CoreError::DagError(format!("Task {} not found in graph {}", task_id, graph_id)))?;

            // Aggiorna lo stato e il timestamp di completamento
            task.metadata.completed_at = Some(chrono::Utc::now());

            match result {
                Ok(value) => {
                    task.state = TaskState::Completed;
                    task.result = Some(value);
                }
                Err(ref e) => {
                    task.state = TaskState::Failed;
                    task.error_message = Some(e.to_string());
                }
            }
        }

        // Emetti un evento per notificare il completamento del task
        let event = Event::custom(
            "dag_engine",
            "task_completed",
            &serde_json::json!({
                "graph_id": graph_id,
                "task_id": task_id,
                "success": result.is_ok(),
            }),
        )?;

        self.event_bus.emit(event)?;

        Ok(())
    }

    /// Elimina un grafo DAG
    pub fn delete_graph(&self, graph_id: &str) -> Result<(), CoreError> {
        let mut graphs = self.graphs.write().map_err(|_| {
            CoreError::LockError("Failed to acquire write lock on graphs".to_string())
        })?;

        if graphs.remove(graph_id).is_none() {
            return Err(CoreError::DagError(format!("Graph with ID {} not found", graph_id)));
        }

        Ok(())
    }
    
    /// Aggiunge un task a un grafo esistente
    pub fn add_task_to_graph(&self, graph_id: &str, task: Task) -> Result<(), CoreError> {
        let graph = self.get_graph(graph_id)?;
        
        let mut graph_guard = graph.write().map_err(|_| {
            CoreError::LockError("Failed to acquire write lock on graph".to_string())
        })?;
        
        graph_guard.add_task(task)?;
        
        // Emetti un evento per notificare l'aggiunta del task
        let event = Event::custom(
            "dag_engine",
            "task_added",
            &serde_json::json!({
                "graph_id": graph_id,
            }),
        )?;
        
        self.event_bus.emit(event)?;
        
        Ok(())
    }
    
    /// Annulla l'esecuzione di un grafo
    pub fn cancel_graph_execution(&self, graph_id: &str) -> Result<(), CoreError> {
        let graph = self.get_graph(graph_id)?;
        
        let mut graph_guard = graph.write().map_err(|_| {
            CoreError::LockError("Failed to acquire write lock on graph".to_string())
        })?;
        
        // Annulla tutti i task in stato Pending o Running
        for (task_id, task) in graph_guard.tasks.iter_mut() {
            if task.state == TaskState::Pending || task.state == TaskState::Running {
                task.state = TaskState::Cancelled;
                
                // Emetti un evento per notificare l'annullamento del task
                let event = Event::custom(
                    "dag_engine",
                    "task_cancelled",
                    &serde_json::json!({
                        "graph_id": graph_id,
                        "task_id": task_id,
                    }),
                )?;
                
                self.event_bus.emit(event)?;
            }
        }
        
        // Emetti un evento per notificare l'annullamento del grafo
        let event = Event::custom(
            "dag_engine",
            "graph_cancelled",
            &serde_json::json!({
                "graph_id": graph_id,
            }),
        )?;
        
        self.event_bus.emit(event)?;
        
        Ok(())
    }
    
    /// Ottiene lo stato di un task
    pub fn get_task_state(&self, graph_id: &str, task_id: &str) -> Result<TaskState, CoreError> {
        let graph = self.get_graph(graph_id)?;
        
        let graph_guard = graph.read().map_err(|_| {
            CoreError::LockError("Failed to acquire read lock on graph".to_string())
        })?;
        
        let task = graph_guard.get_task(task_id)
            .ok_or_else(|| CoreError::DagError(format!("Task {} not found in graph {}", task_id, graph_id)))?;
            
        Ok(task.state)
    }
    
    /// Ottiene il risultato di un task
    pub fn get_task_result(&self, graph_id: &str, task_id: &str) -> Result<Option<serde_json::Value>, CoreError> {
        let graph = self.get_graph(graph_id)?;
        
        let graph_guard = graph.read().map_err(|_| {
            CoreError::LockError("Failed to acquire read lock on graph".to_string())
        })?;
        
        let task = graph_guard.get_task(task_id)
            .ok_or_else(|| CoreError::DagError(format!("Task {} not found in graph {}", task_id, graph_id)))?;
        
        if task.state != TaskState::Completed {
            return Err(CoreError::DagError(format!("Task {} has not completed successfully (current state: {:?})", task_id, task.state)));
        }
            
        Ok(task.result.clone())
    }
    
    /// Rimuove un grafo
    pub fn remove_graph(&self, graph_id: &str) -> Result<(), CoreError> {
        let mut graphs = self.graphs.write().map_err(|_| {
            CoreError::LockError("Failed to acquire write lock on graphs".to_string())
        })?;
        
        if !graphs.contains_key(graph_id) {
            return Err(CoreError::DagError(format!("Graph with ID {} not found", graph_id)));
        }
        
        graphs.remove(graph_id);
        
        // Emetti un evento per notificare la rimozione del grafo
        let event = Event::custom(
            "dag_engine",
            "graph_removed",
            &serde_json::json!({
                "graph_id": graph_id,
            }),
        )?;
        
        self.event_bus.emit(event)?;
        
        Ok(())
    }
    /// Aggiunge un task a un grafo esistente
    pub fn add_task(&self, graph_id: &str, task: Task) -> Result<(), CoreError> {
        let graph = self.get_graph(graph_id)?;
        
        let mut graph_guard = graph.write().map_err(|_| {
            CoreError::LockError("Failed to acquire write lock on graph".to_string())
        })?;
        
        graph_guard.add_task(task)?;
        
        // Emetti un evento per notificare l'aggiunta del task
        let event = Event::custom(
            "dag_engine",
            "task_added",
            &serde_json::json!({
                "graph_id": graph_id,
            }),
        )?;
        
        self.event_bus.emit(event)?;
        
        Ok(())
    }
    

        
        let task = graph_guard.get_task(task_id)
            .ok_or_else(|| CoreError::DagError(format!("Task {} not found in graph {}", task_id, graph_id)))?;
            
        Ok(task.state)
    }
}

impl DagEngine {
    /// Annulla l'esecuzione di un grafo
    pub fn cancel_graph(&self, graph_id: &str) -> Result<(), CoreError> {
        let graph = self.get_graph(graph_id)?;
        
        let mut graph_guard = graph.write().map_err(|_| {
            CoreError::LockError("Failed to acquire write lock on graph".to_string())
        })?;
        
        // Annulla tutti i task in stato Pending o Running
        for (task_id, task) in graph_guard.tasks.iter_mut() {
            if task.state == TaskState::Pending || task.state == TaskState::Running {
                task.state = TaskState::Cancelled;
                
                // Emetti un evento per notificare l'annullamento del task
                let event = Event::custom(
                    "dag_engine",
                    "task_cancelled",
                    &serde_json::json!({
                        "graph_id": graph_id,
                        "task_id": task_id,
                    }),
                )?;
                
                self.event_bus.emit(event)?;
            }
        }
        
        // Emetti un evento per notificare l'annullamento del grafo
        let event = Event::custom(
            "dag_engine",
            "graph_cancelled",
            &serde_json::json!({
                "graph_id": graph_id,
            }),
        )?;
        
        self.event_bus.emit(event)?;
        
        Ok(())
    }
    
    /// Ottiene tutti i task di un grafo
    pub fn get_all_tasks(&self, graph_id: &str) -> Result<Vec<Task>, CoreError> {
        let graph = self.get_graph(graph_id)?;
        
        let graph_guard = graph.read().map_err(|_| {
            CoreError::LockError("Failed to acquire read lock on graph".to_string())
        })?;
        
        Ok(graph_guard.tasks.values().cloned().collect())
    }
    
    /// Ottiene un singolo task da un grafo
    pub fn get_task(&self, graph_id: &str, task_id: &str) -> Result<Task, CoreError> {
        let graph = self.get_graph(graph_id)?;
        
        let graph_guard = graph.read().map_err(|_| {
            CoreError::LockError("Failed to acquire read lock on graph".to_string())
        })?;
        
        graph_guard.get_task(task_id)
            .cloned()
            .ok_or_else(|| CoreError::DagError(format!("Task {} not found in graph {}", task_id, graph_id)))
    }
    
    /// Aggiorna lo stato di un task
    pub fn update_task_state(&self, graph_id: &str, task_id: &str, state: TaskState) -> Result<(), CoreError> {
        let graph = self.get_graph(graph_id)?;
        
        let mut graph_guard = graph.write().map_err(|_| {
            CoreError::LockError("Failed to acquire write lock on graph".to_string())
        })?;
        
        let task = graph_guard.get_task_mut(task_id)
            .ok_or_else(|| CoreError::DagError(format!("Task {} not found in graph {}", task_id, graph_id)))?;
            
        let old_state = task.state;
        task.state = state;
        
        // Se lo stato è cambiato, aggiorna i timestamp appropriati
        if old_state != state {
            match state {
                TaskState::Running => {
                    task.metadata.started_at = Some(chrono::Utc::now());
                },
                TaskState::Completed | TaskState::Failed | TaskState::Cancelled => {
                    task.metadata.completed_at = Some(chrono::Utc::now());
                },
                _ => {}
            }
            
            // Emetti un evento per notificare il cambio di stato
            let event = Event::custom(
                "dag_engine",
                "task_state_changed",
                &serde_json::json!({
                    "graph_id": graph_id,
                    "task_id": task_id,
                    "old_state": format!("{:?}", old_state),
                    "new_state": format!("{:?}", state),
                }),
            )?;
            
            self.event_bus.emit(event)?;
        }
        
        Ok(())
    }
}