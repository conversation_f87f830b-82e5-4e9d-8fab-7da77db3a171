use crate::{EventBus, Event};
use std::collections::{HashMap, VecDeque, HashSet};
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;
use serde::{Serialize, Deserialize};
use anyhow::{Result, Error as AnyhowError};

/// Rappresenta un micro-task atomico nel sistema
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MicroTask {
    pub id: Uuid,
    pub task_type: TaskType,
    pub input: TaskInput,
    pub output: Option<TaskOutput>,
    pub dependencies: Vec<Uuid>,
    pub status: TaskStatus,
    pub priority: TaskPriority,
    pub rollback_plan: Option<RollbackPlan>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub estimated_duration: std::time::Duration,
}

/// Tipi di task supportati dal sistema
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum TaskType {
    // Code generation tasks
    GenerateFunction { signature: String, requirements: String },
    GenerateClass { name: String, methods: Vec<String> },
    GenerateModule { name: String, exports: Vec<String> },

    // Refactoring tasks
    RenameSymbol { old_name: String, new_name: String },
    ExtractMethod { code_range: (usize, usize), new_name: String },
    InlineMethod { method_name: String },

    // Dependency management
    AddDependency { package: String, version: String },
    UpdateDependency { package: String, from_version: String, to_version: String },
    RemoveDependency { package: String },

    // Testing tasks
    GenerateTests { target_function: String },
    RunTests { test_suite: String },

    // Documentation tasks
    GenerateDocumentation { target: String },
    UpdateReadme { sections: Vec<String> },

    // File operations
    CreateFile { path: String, content: String },
    ModifyFile { path: String, changes: Vec<FileChange> },
    DeleteFile { path: String },
}

/// Input per un micro-task
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskInput {
    pub context: TaskContext,
    pub parameters: HashMap<String, serde_json::Value>,
    pub source_files: Vec<String>,
}

/// Output di un micro-task
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskOutput {
    pub result: TaskResult,
    pub generated_files: Vec<GeneratedFile>,
    pub warnings: Vec<String>,
    pub execution_time: std::time::Duration,
}

/// Contesto di esecuzione per un task
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskContext {
    pub project_root: String,
    pub current_file: Option<String>,
    pub cursor_position: Option<(usize, usize)>,
    pub selected_text: Option<String>,
    pub open_files: Vec<String>,
    pub git_branch: Option<String>,
    pub language: ProgrammingLanguage,
}

/// Status di un micro-task
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TaskStatus {
    Created,
    Queued,
    Running,
    Completed,
    Failed { error: String },
    Cancelled,
    RolledBack,
}

/// Priorità di esecuzione
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, PartialOrd)]
pub enum TaskPriority {
    Low = 1,
    Normal = 2,
    High = 3,
    Critical = 4,
    Immediate = 5,
}

/// Piano di rollback per un task
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RollbackPlan {
    pub rollback_type: RollbackType,
    pub backup_data: Option<serde_json::Value>,
    pub rollback_tasks: Vec<Uuid>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum RollbackType {
    RestoreFile { original_content: String },
    RemoveFile { file_path: String },
    RestoreDependency { package: String, version: String },
    UndoRefactoring { original_code: String, file_path: String },
}

/// Risultato dell'esecuzione di un task
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TaskResult {
    Success { message: String },
    Partial { completed_items: Vec<String>, failed_items: Vec<String> },
    Failed { error: String, suggestions: Vec<String> },
}

/// File generato da un task
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeneratedFile {
    pub path: String,
    pub content: String,
    pub file_type: FileType,
    pub should_open: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FileType {
    Source,
    Test,
    Documentation,
    Configuration,
}

/// Modifica a un file
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct FileChange {
    pub change_type: ChangeType,
    pub line_range: (usize, usize),
    pub old_content: String,
    pub new_content: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ChangeType {
    Insert,
    Replace,
    Delete,
}

/// Linguaggio di programmazione
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ProgrammingLanguage {
    Rust,
    TypeScript,
    JavaScript,
    Python,
    Go,
    Java,
    CSharp,
    Cpp,
    Other(String),
}

// Usa il tipo reale dal modulo chain_reaction_engine
use crate::chain_reaction_engine::ChainReactionEngine;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MicroTaskFabricConfig {
    pub max_concurrent_tasks: usize,
    pub task_timeout: std::time::Duration,
    pub auto_rollback_on_failure: bool,
    pub enable_dependency_analysis: bool,
    pub max_queue_size: usize,
}

impl Default for MicroTaskFabricConfig {
    fn default() -> Self {
        MicroTaskFabricConfig {
            max_concurrent_tasks: 4,
            task_timeout: std::time::Duration::from_secs(60),
            auto_rollback_on_failure: true,
            enable_dependency_analysis: true,
            max_queue_size: 1000,
        }
    }
}

/// Core del sistema Micro-Task Fabric
pub struct MicroTaskFabric {
    /// Tutti i task nel sistema, indicizzati per ID
    tasks: Arc<RwLock<HashMap<Uuid, MicroTask>>>,

    /// Coda di esecuzione ordinata per priorità
    execution_queue: Arc<RwLock<VecDeque<Uuid>>>,

    /// Task attualmente in esecuzione
    running_tasks: Arc<RwLock<HashSet<Uuid>>>,

    /// Grafo delle dipendenze tra task
    dependency_graph: Arc<RwLock<HashMap<Uuid, HashSet<Uuid>>>>,

    /// Event bus per comunicazione asincrona
    event_bus: Arc<EventBus>,

    /// State manager per persistenza
    state_manager: Arc<()>,

    /// Chain reaction engine per analisi impatti
    chain_reaction_engine: Arc<ChainReactionEngine>,

    /// Configurazione del fabric
    config: MicroTaskFabricConfig,
}

impl MicroTaskFabric {
    /// Crea una nuova istanza di MicroTaskFabric
    pub fn new(
        event_bus: Arc<EventBus>,
        state_manager: Arc<()>,
        chain_reaction_engine: Arc<ChainReactionEngine>,
        config: MicroTaskFabricConfig,
    ) -> Self {
        Self {
            tasks: Arc::new(RwLock::new(HashMap::new())),
            execution_queue: Arc::new(RwLock::new(VecDeque::new())),
            running_tasks: Arc::new(RwLock::new(HashSet::new())),
            dependency_graph: Arc::new(RwLock::new(HashMap::new())),
            event_bus,
            state_manager,
            chain_reaction_engine,
            config,
        }
    }

    /// Getter pubblico per accedere ai tasks (necessario per API e test)
    pub fn tasks(&self) -> &Arc<RwLock<HashMap<Uuid, MicroTask>>> {
        &self.tasks
    }

    /// Getter per dependency_graph
    pub fn dependency_graph(&self) -> &Arc<RwLock<HashMap<Uuid, HashSet<Uuid>>>> {
        &self.dependency_graph
    }

    /// Getter per event_bus
    pub fn event_bus(&self) -> &Arc<EventBus> {
        &self.event_bus
    }

    /// Getter per chain_reaction_engine
    pub fn chain_reaction_engine(&self) -> &Arc<ChainReactionEngine> {
        &self.chain_reaction_engine
    }

    /// Implementazione semplificata di ordinamento topologico
    fn simple_topological_sort(
        &self,
        graph: &HashMap<Uuid, HashSet<Uuid>>,
        tasks: &[MicroTask],
    ) -> Result<Vec<Uuid>> {
        // Per ora, restituisce semplicemente l'ordine dei task
        // In futuro, implementare algoritmo di Kahn o DFS
        Ok(tasks.iter().map(|t| t.id).collect())
    }
}





// Implementazione di TaskDependencyGraph
struct TaskDependencyGraph {
    // Struttura dati per rappresentare il grafo
    nodes: HashMap<Uuid, TaskType>,
    edges: HashMap<Uuid, Vec<Uuid>>,
}

impl TaskDependencyGraph {
    fn new() -> Self {
        Self {
            nodes: HashMap::new(),
            edges: HashMap::new(),
        }
    }

    fn add_task(&mut self, task_id: Uuid, task_type: TaskType) {
        self.nodes.insert(task_id, task_type);
        if !self.edges.contains_key(&task_id) {
            self.edges.insert(task_id, Vec::new());
        }
    }

    fn add_dependency(&mut self, from_task: Uuid, to_task: Uuid) -> Result<()> {
        if !self.nodes.contains_key(&from_task) || !self.nodes.contains_key(&to_task) {
            return Err(AnyhowError::msg("Task non trovato nel grafo delle dipendenze"));
        }

        self.edges.entry(from_task).or_default().push(to_task);

        // Verifica che non si creino cicli
        if self.has_cycles() {
            // Rimuovi l'arco appena aggiunto
            let deps = self.edges.get_mut(&from_task).unwrap();
            deps.retain(|&id| id != to_task);
            return Err(AnyhowError::msg("La dipendenza creerebbe un ciclo nel grafo"));
        }

        Ok(())
    }

    fn has_cycles(&self) -> bool {
        let mut visited = HashSet::new();
        let mut rec_stack = HashSet::new();

        for &node in self.nodes.keys() {
            if !visited.contains(&node) {
                if self.is_cyclic_util(node, &mut visited, &mut rec_stack) {
                    return true;
                }
            }
        }

        false
    }

    fn is_cyclic_util(&self, node: Uuid, visited: &mut HashSet<Uuid>, rec_stack: &mut HashSet<Uuid>) -> bool {
        visited.insert(node);
        rec_stack.insert(node);

        if let Some(neighbors) = self.edges.get(&node) {
            for &neighbor in neighbors {
                if !visited.contains(&neighbor) {
                    if self.is_cyclic_util(neighbor, visited, rec_stack) {
                        return true;
                    }
                } else if rec_stack.contains(&neighbor) {
                    return true;
                }
            }
        }

        rec_stack.remove(&node);
        false
    }

    fn topological_sort(&self) -> Result<Vec<Uuid>> {
        let mut result = Vec::new();
        let mut visited = HashSet::new();
        let mut temp_mark = HashSet::new();

        // Lista di nodi da visitare (tutti i nodi)
        let mut nodes: Vec<_> = self.nodes.keys().cloned().collect();

        // Ordina per priorità se disponibile
        // Qui potremmo ordinare basandoci sul tipo di task

        for node in nodes {
            if !visited.contains(&node) {
                self.visit(&node, &mut visited, &mut temp_mark, &mut result)?;
            }
            
            // Esponi i moduli pubblici
            pub mod task {
                pub use super::{
                    MicroTask, TaskType, TaskInput, TaskOutput, TaskContext,
                    TaskStatus, TaskPriority, RollbackPlan, TaskResult,
                    GeneratedFile, FileType, FileChange, ChangeType,
                    ProgrammingLanguage, RollbackType
                };
            }
        }

        // Inverti il risultato perché vogliamo eseguire dalla radice alle foglie
        result.reverse();
        Ok(result)
    }

    fn visit(
        &self,
        node: &Uuid,
        visited: &mut HashSet<Uuid>,
        temp_mark: &mut HashSet<Uuid>,
        result: &mut Vec<Uuid>,
    ) -> Result<()> {
        if temp_mark.contains(node) {
            return Err(AnyhowError::msg("Grafo ciclico rilevato durante l'ordinamento topologico"));
        }

        if !visited.contains(node) {
            temp_mark.insert(*node);

            if let Some(deps) = self.edges.get(node) {
                for dep in deps {
                    self.visit(dep, visited, temp_mark, result)?;
                }
            }

            temp_mark.remove(node);
            visited.insert(*node);
            result.push(*node);
        }

        Ok(())
    }
}

/// Definizione del risultato MVP
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MVPResult {
    Success {
        generated_files: Vec<GeneratedFile>,
        execution_summary: String,
    },
    Partial {
        generated_files: Vec<GeneratedFile>,
        failed_tasks: Vec<Uuid>,
        error_message: String,
    },
    ChainReactionBlocked {
        blocked_task: MicroTask,
        impact_analysis: ChainReactionAnalysis,
        alternatives: Vec<AlternativeApproach>,
    },
    Failed {
        error_message: String,
        failed_task: Option<Uuid>,
    },
}

/// Analisi di chain reaction
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChainReactionAnalysis {
    pub is_safe: bool,
    pub affected_components: Vec<String>,
    pub impact_severity: ImpactSeverity,
    pub visualization_data: String, // JSON per visualizzazione grafo
    pub recommendation: String,
}

impl ChainReactionAnalysis {
    pub fn is_safe(&self) -> bool {
        self.is_safe
    }
}

/// Severità dell'impatto
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ImpactSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Approccio alternativo
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlternativeApproach {
    pub description: String,
    pub alternative_task: MicroTask,
    pub predicted_impact: ChainReactionAnalysis,
}

/// Risultato dell'esecuzione del DAG
#[derive(Debug, Clone)]
pub struct DAGExecutionResult {
    pub completed_tasks: Vec<(Uuid, TaskResult)>,
    pub failed_tasks: Vec<(Uuid, String)>,
    pub execution_time: std::time::Duration,



}

impl MicroTaskFabric {
    /// Orchestrazione principale: da prompt a MVP completo
    pub async fn orchestrate_prompt_to_mvp(&self, prompt: &str, context: TaskContext) -> Result<MVPResult> {
        log::info!("🚀 Iniziando orchestrazione prompt to MVP: {}", prompt);

        // 1. Analizza e decompone il prompt in micro-task
        let tasks = self.decompose_prompt_to_tasks(prompt, &context).await?;
        log::info!("📋 Decomposizione completata: {} task generati", tasks.len());

        // 2. Verifica chain reaction per ogni task
        for task in &tasks {
            let impact_analysis = self.analyze_task_impact(task).await?;

            if !impact_analysis.is_safe() {
                log::warn!("⚠️ Chain reaction non sicura rilevata per task {}", task.id);
                let alternatives = self.generate_safe_alternatives(task, &impact_analysis).await?;
                return Ok(MVPResult::ChainReactionBlocked {
                    blocked_task: task.clone(),
                    impact_analysis,
                    // [FIX] Prima usiamo il borrow per alternatives, poi muoviamo impact_analysis.
                    alternatives,
                });
            }
        }

        // 3. Crea grafo delle dipendenze
        self.build_dependency_graph(&tasks).await?;

        // 4. Esegui orchestrazione DAG
        let execution_result = self.execute_task_dag(tasks).await?;

        // 5. Genera risultato MVP
        let mvp_result = self.compile_mvp_result(execution_result).await?;

        log::info!("✅ Orchestrazione MVP completata con successo");
        Ok(mvp_result)
    }

    /// Analizza l'impatto di un task (stub temporaneo)
    async fn analyze_task_impact(&self, task: &MicroTask) -> Result<ChainReactionAnalysis> {
        // Chiamata temporanea alla Chain Reaction Engine
        // In futuro, questa sarà un'implementazione completa
        Ok(ChainReactionAnalysis {
            is_safe: true, // Considera tutti i task sicuri per ora
            affected_components: vec![],
            impact_severity: ImpactSeverity::Low,
            visualization_data: "{}".to_string(),
            recommendation: "Nessun impatto rilevato".to_string(),
        })
    }

    /// Genera alternative sicure per un task (stub temporaneo)
    async fn generate_safe_alternatives(
        &self,
        task: &MicroTask,
        impact: &ChainReactionAnalysis,
    ) -> Result<Vec<AlternativeApproach>> {
        // Implementazione semplificata
        let mut alternative = task.clone();
        alternative.id = Uuid::new_v4();

        Ok(vec![AlternativeApproach {
            description: "Versione semplificata con meno impatto".to_string(),
            alternative_task: alternative,
            predicted_impact: ChainReactionAnalysis {
                is_safe: true,
                affected_components: vec![],
                impact_severity: ImpactSeverity::Low,
                visualization_data: "{}".to_string(),
                recommendation: "Alternative sicura".to_string(),
            },
        }])
    }

    /// Decompone un prompt in una serie di micro-task atomici (stub temporaneo)
    async fn decompose_prompt_to_tasks(&self, prompt: &str, context: &TaskContext) -> Result<Vec<MicroTask>> {
        log::debug!("🧠 Analizzando prompt per decomposizione: {}", prompt);

        // Questa è una versione semplificata - in futuro integreremo l'AI engine
        let mut tasks = Vec::new();

        // Per ora, creiamo un singolo task generico
        tasks.push(MicroTask {
            id: Uuid::new_v4(),
            task_type: TaskType::GenerateFunction {
                signature: "fn example() -> Result<()>".to_string(),
                requirements: prompt.to_string(),
            },
            input: TaskInput {
                context: context.clone(),
                parameters: HashMap::new(),
                source_files: context.open_files.clone(),
            },
            output: None,
            dependencies: vec![],
            status: TaskStatus::Created,
            priority: TaskPriority::Normal,
            rollback_plan: None,
            created_at: chrono::Utc::now(),
            estimated_duration: std::time::Duration::from_secs(30),
        });

        Ok(tasks)
    }

    /// Costruisce il grafo delle dipendenze tra task
    async fn build_dependency_graph(&self, tasks: &[MicroTask]) -> Result<()> {
        let mut graph = self.dependency_graph.write().await;

        for task in tasks {
            // Inizializza il set di dipendenze per questo task
            graph.insert(task.id, HashSet::new());

            // Aggiungi dipendenze esplicite
            for dep_id in &task.dependencies {
                if let Some(deps) = graph.get_mut(&task.id) {
                    deps.insert(*dep_id);
                }
            }
        }

        Ok(())
    }

    /// Esegue il DAG di task
    async fn execute_task_dag(&self, tasks: Vec<MicroTask>) -> Result<DAGExecutionResult> {
        log::info!("⚙️ Iniziando esecuzione DAG con {} task", tasks.len());

        // Registra tutti i task
        {
            let mut task_map = self.tasks.write().await;
            for task in tasks {
                task_map.insert(task.id, task);
            }
        }

        // Ordina i task topologicamente
        let execution_order = {
            let graph = self.dependency_graph.read().await;
            graph.topological_sort()?
        };

        let mut completed_tasks = Vec::new();
        let mut failed_tasks = Vec::new();

        // Stub temporaneo: considera tutti i task come completati con successo
        for task_id in execution_order {
            if let Some(task) = self.tasks.read().await.get(&task_id) {
                completed_tasks.push((task_id, TaskResult::Success {
                    message: format!("Task {} completato con successo", task_id)
                }));
            }
        }

        Ok(DAGExecutionResult {
            completed_tasks,
            failed_tasks,
            execution_time: std::time::Duration::from_secs(1),
        })
    }

    /// Compila il risultato MVP dai risultati dell'esecuzione
    async fn compile_mvp_result(&self, result: DAGExecutionResult) -> Result<MVPResult> {
        if result.failed_tasks.is_empty() {
            Ok(MVPResult::Success {
                generated_files: vec![], // Placeholder
                execution_summary: format!(
                    "Completati con successo {} task in {:?}",
                    result.completed_tasks.len(),
                    result.execution_time
                ),
            })
        } else {
            Ok(MVPResult::Partial {
                generated_files: vec![], // Placeholder
                failed_tasks: result.failed_tasks.iter().map(|(id, _)| *id).collect(),
                error_message: result.failed_tasks.iter()
                    .map(|(_, error)| error.clone())
                    .collect::<Vec<_>>()
                    .join("; "),
            })
        }
    }








}
